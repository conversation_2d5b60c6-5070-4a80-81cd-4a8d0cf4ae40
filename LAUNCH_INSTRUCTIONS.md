# 🚀 TELEGRAM CLOUD - ИНСТРУКЦИИ ПО ЗАПУСКУ

Доработанные скрипты для запуска и монтирования Telegram облачного диска одним кликом.

## 📁 Доступные скрипты

### 1. **START_TELEGRAM_CLOUD.bat** ⭐ РЕКОМЕНДУЕТСЯ
**Самый простой и быстрый запуск**
- ✅ Максимальная простота использования
- ✅ Автопоиск свободной буквы диска
- ✅ Автоматическое открытие диска в проводнике
- ✅ Минимум проверок для быстрого старта

**Использование:** Просто дважды кликните по файлу

---

### 2. **launch_and_mount.bat**
**Полнофункциональный запуск с проверками**
- ✅ Полная проверка всех зависимостей
- ✅ Автоматическая установка недостающих компонентов
- ✅ Красивый интерфейс с прогресс-индикацией
- ✅ Создание ярлыков на рабочем столе
- ✅ Интерактивные опции

**Особенности:**
- Проверяет и предлагает установить WinFsp
- Проверяет и предлагает установить rclone
- Настраивает конфигурацию rclone
- Мониторинг состояния сервисов

---

### 3. **quick_start.bat**
**Упрощенный запуск без лишних проверок**
- ✅ Быстрый старт без долгих проверок
- ✅ Простое меню действий
- ✅ Поддержка веб-интерфейса
- ✅ Базовый мониторинг

---

### 4. **autostart_simple.bat** (ОБНОВЛЕН)
**Настройка автозапуска при старте Windows**
- ✅ Создает задачу автозапуска в Windows
- ✅ Автоматическое монтирование диска при загрузке
- ✅ Работа в фоновом режиме
- ✅ Поддержка WebDAV + rclone

**Что делает:**
- Создает файл `TelegramCloudAutostart.bat` в папке автозагрузки
- При каждом запуске Windows автоматически запускает Telegram Cloud
- Ищет свободную букву диска (T, G, H, X, Y, Z)
- Запускает WebDAV сервер и монтирует диск

---

### 5. **one_click_mount.py** (В РАЗРАБОТКЕ)
**Python скрипт с графическим интерфейсом**
- 🔄 GUI интерфейс на tkinter
- 🔄 Расширенные настройки
- 🔄 Мониторинг процессов
- 🔄 Автоматическая диагностика

---

## 🔧 ПЕРВОНАЧАЛЬНАЯ НАСТРОЙКА

### Шаг 1: Убедитесь, что выполнена базовая настройка
```bash
# 1. Создано виртуальное окружение
python -m venv venv

# 2. Установлены зависимости
venv\Scripts\pip install -r requirements.txt

# 3. Настроен config.env (скопируйте из config_example.env)
copy config_example.env config.env
# Отредактируйте config.env - укажите ваши данные Telegram
```

### Шаг 2: Установите необходимые компоненты (если нужно)
```bash
# WinFsp (для монтирования дисков в Windows)
python install_winfsp.py

# rclone (для работы с облачными хранилищами)
python install_rclone.py

# Настройка rclone для Telegram WebDAV
python rclone_setup.py
```

---

## 🚀 БЫСТРЫЙ СТАРТ

### Вариант 1: Самый простой (РЕКОМЕНДУЕТСЯ)
1. Дважды кликните `START_TELEGRAM_CLOUD.bat`
2. Дождитесь сообщения "ГОТОВО!"
3. Диск появится в проводнике автоматически

### Вариант 2: С полными проверками
1. Дважды кликните `launch_and_mount.bat`
2. Следуйте инструкциям на экране
3. Выберите опции (ярлык, автооткрытие)

### Вариант 3: Настройка автозапуска
1. Дважды кликните `autostart_simple.bat`
2. Согласитесь на создание автозапуска
3. При следующем запуске Windows диск будет доступен автоматически

---

## 📍 ИСПОЛЬЗОВАНИЕ

### После успешного запуска:

**Диск в проводнике:**
- Откройте "Этот компьютер"
- Найдите новый диск (обычно T:, G:, H:, X:, Y: или Z:)
- Используйте как обычный диск

**WebDAV доступ:**
- Адрес: `http://localhost:8080`
- Можно подключить в любом файловом менеджере
- Поддержка загрузки/скачивания через HTTP

**Веб-интерфейс:**
- Запустите: `python run.py`
- Откройте: `http://localhost:3000`

---

## 🛠️ УСТРАНЕНИЕ ПРОБЛЕМ

### Проблема: "Виртуальное окружение не найдено"
**Решение:**
```bash
python setup.py
# или
python -m venv venv
venv\Scripts\pip install -r requirements.txt
```

### Проблема: "WinFsp не установлен"
**Решение:**
```bash
python install_winfsp.py
# или скачайте с https://winfsp.dev/rel/
```

### Проблема: "rclone не найден"
**Решение:**
```bash
python install_rclone.py
# или скачайте с https://rclone.org/downloads/
```

### Проблема: "Конфигурация rclone не найдена"
**Решение:**
```bash
python rclone_setup.py
```

### Проблема: "Файл config.env не найден"
**Решение:**
```bash
copy config_example.env config.env
notepad config.env
# Укажите ваши данные Telegram API
```

### Проблема: "Диск не монтируется"
**Возможные причины:**
- Буква диска занята → скрипт автоматически найдет свободную
- WinFsp не установлен → запустите `install_winfsp.py`
- rclone не настроен → запустите `rclone_setup.py`
- WebDAV сервер не запустился → проверьте config.env

---

## 💡 ДОПОЛНИТЕЛЬНЫЕ ВОЗМОЖНОСТИ

### Остановка сервисов:
- Закройте окно командной строки (Ctrl+C)
- Или завершите процессы `python.exe` и `rclone.exe` в диспетчере задач

### Ручной запуск компонентов:
```bash
# Только WebDAV сервер
python telegram_webdav.py

# Только монтирование диска (требует запущенный WebDAV)
rclone mount telegram: T: --vfs-cache-mode writes

# Веб-интерфейс
python run.py
```

### Настройка буквы диска:
Отредактируйте в скрипте строку:
```batch
for %%d in (T G H X Y Z) do (
```
Замените буквы на нужные вам.

---

## 📋 СИСТЕМНЫЕ ТРЕБОВАНИЯ

- **ОС:** Windows 10/11
- **Python:** 3.7+
- **Зависимости:** устанавливаются автоматически
- **WinFsp:** устанавливается автоматически при необходимости
- **rclone:** скачивается автоматически при необходимости

---

## 🔐 БЕЗОПАСНОСТЬ

- Все данные передаются через зашифрованное соединение Telegram
- Локальный WebDAV сервер доступен только с вашего компьютера
- Файлы хранятся в вашем Telegram аккаунте/боте
- Никакие данные не передаются третьим лицам

---

## 📞 ПОДДЕРЖКА

При возникновении проблем:
1. Проверьте файл `config.env`
2. Убедитесь в корректности данных Telegram API
3. Проверьте доступ к интернету
4. Перезапустите скрипт от имени администратора

---

**🎉 Готово! Теперь у вас есть полноценный облачный диск на базе Telegram!** 