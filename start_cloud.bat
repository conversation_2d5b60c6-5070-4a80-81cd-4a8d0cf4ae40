@echo off
chcp 65001 >nul

REM ------------------------------------------------------------
REM  Telegram Cloud Drive – Автоматический запуск
REM  1) Запускает WebDAV-сервер в фоновом окне
REM  2) Монтирует Telegram как диск T:
REM ------------------------------------------------------------

REM Переходим в папку скрипта
cd /d "%~dp0"

echo 🔄 Активация виртуального окружения...
if exist "venv\Scripts\activate.bat" (
    call "venv\Scripts\activate.bat"
) else (
    echo ⚠️  venv не найден, используем системный Python
)

REM Проверяем, запущен ли уже WebDAV (порт 8080)
netstat -ano | find "LISTENING" | find ":8080" >nul 2>&1
if %errorlevel%==0 (
    echo 🟢 WebDAV уже слушает порт 8080
) else (
    echo 🚀 Запуск WebDAV-сервера...
    start "WebDAV" cmd /c "python telegram_webdav.py"
    timeout /t 5 >nul
)

REM Определяем путь к rclone
where rclone >nul 2>&1
if errorlevel 1 (
    if exist "C:\Program Files\rclone\rclone.exe" (
        set "RCLONE_EXE=C:\Program Files\rclone\rclone.exe"
    ) else (
        echo ❌ rclone.exe не найден! Установите rclone или добавьте в PATH.
        pause
        exit /b 1
    )
) else (
    for /f %%i in ('where rclone') do (
        set "RCLONE_EXE=%%i"
        goto :after_rclone
    )
)
:after_rclone

REM Проверяем, смонтирован ли уже диск T:
if exist T:\ (
    echo 🟢 Диск T: уже смонтирован.
    goto :end
)

echo 💿 Монтирование диска T: ...
start "rcloneMount" cmd /c "%RCLONE_EXE%" mount telegram: T: --vfs-cache-mode writes --vfs-cache-max-size 1G --vfs-cache-max-age 1h --buffer-size 32M -v

echo ✅ Готово! Диск T: должен появиться в Проводнике.

:end
pause 