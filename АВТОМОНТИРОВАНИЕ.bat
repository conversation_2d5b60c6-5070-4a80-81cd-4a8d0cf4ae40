@echo off
chcp 65001 >nul
title TelegramCloudBasic - Автомонтирование диска

cd /d "%~dp0"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                TelegramCloudBasic                            ║
echo ║              Автомонтирование диска                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Проверяем все компоненты
if not exist "venv\Scripts\python.exe" (
    echo ❌ Виртуальное окружение не найдено!
    echo 📝 Сначала запустите setup.py для установки
    pause
    exit /b 1
)

if not exist "config.env" (
    echo ❌ Файл config.env не найден!
    echo 📝 Скопируйте config_example.env в config.env и настройте
    pause
    exit /b 1
)

if not exist "rclone.exe" (
    echo ❌ rclone.exe не найден!
    echo 📝 Запустите: python install_rclone.py
    pause
    exit /b 1
)

echo ✅ Все компоненты готовы
echo.
echo 🚀 Запуск автомонтирования...
echo.

call auto_mount.bat

echo.
echo 🔚 Автомонтирование завершено
pause
