# Telegram API настройки (обязательно)
# Получите на https://my.telegram.org
TG_API_ID=25936043
TG_API_HASH=080f4e94bb7e4bf8881e4163859f4fec

# Telegram Bot настройки (опционально, альтернатива user API)
# Получите у @BotFather
TG_BOT_TOKEN=8082967180:AAEtUPfDyVjF9SF1oSrd-y5IMTX9KXAux20
# TG_PHONE=+7XXXXXXXXXX

# Целевой чат для хранения файлов
# Может быть: 'me', @username, или ID канала/группы
# ВАЖНО: Для Bot API нельзя использовать самого бота!
# Создайте приватный канал и добавьте туда бота как администратора
# Затем замените на реальный username вашего канала
TG_DEST_CHAT=@myfilesfoor

# Порт WebDAV сервера
WEBDAV_PORT=8080

# Максимальный размер файла в MB (по умолчанию 50MB)
# Telegram поддерживает до 2GB для ботов
MAX_FILE_SIZE=2000

# Настройки веб-интерфейса
WEB_PORT=8000
WEB_HOST=127.0.0.1 