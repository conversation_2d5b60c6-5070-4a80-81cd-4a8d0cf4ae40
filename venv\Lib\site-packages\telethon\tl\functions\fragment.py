"""File generated by TLObjects' generator. All changes will be ERASED"""
from ...tl.tlobject import TLObject
from ...tl.tlobject import TLRe<PERSON>
from typing import Optional, List, Union, TYPE_CHECKING
import os
import struct
from datetime import datetime
if TYPE_CHECKING:
    from ...tl.types import TypeInputCollectible



class GetCollectibleInfoRequest(TLRequest):
    CONSTRUCTOR_ID = 0xbe1e85ba
    SUBCLASS_OF_ID = 0xd4ea5790

    def __init__(self, collectible: 'TypeInputCollectible'):
        """
        :returns fragment.CollectibleInfo: Instance of CollectibleInfo.
        """
        self.collectible = collectible

    def to_dict(self):
        return {
            '_': 'GetCollectibleInfoRequest',
            'collectible': self.collectible.to_dict() if isinstance(self.collectible, TLObject) else self.collectible
        }

    def _bytes(self):
        return b''.join((
            b'\xba\x85\x1e\xbe',
            self.collectible._bytes(),
        ))

    @classmethod
    def from_reader(cls, reader):
        _collectible = reader.tgread_object()
        return cls(collectible=_collectible)

