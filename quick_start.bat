@echo off
title Telegram Cloud - Быстрый запуск
color 0B

echo.
echo ⚡ TELEGRAM CLOUD - БЫСТРЫЙ ЗАПУСК ⚡
echo ===================================
echo.

REM Переход в директорию скрипта
cd /d "%~dp0"

REM Проверка основных файлов
if not exist "telegram_webdav.py" (
    echo ❌ telegram_webdav.py не найден!
    pause
    exit /b 1
)

if not exist "venv\Scripts\activate.bat" (
    echo ❌ Виртуальное окружение не найдено!
    echo Запустите setup.py для установки
    pause
    exit /b 1
)

REM Активация виртуального окружения
call "venv\Scripts\activate.bat"

REM Поиск свободной буквы диска
set DRIVE_LETTER=T
if exist "T:\" set DRIVE_LETTER=G
if exist "G:\" set DRIVE_LETTER=H
if exist "H:\" set DRIVE_LETTER=X
if exist "X:\" set DRIVE_LETTER=Y
if exist "Y:\" set DRIVE_LETTER=Z

echo 🚀 Запуск компонентов...
echo    💾 Диск: %DRIVE_LETTER%:
echo    🌐 WebDAV: http://localhost:8080
echo.

REM Запуск WebDAV сервера в фоне
echo 🌐 Запуск WebDAV сервера...
start "Telegram WebDAV" /min python telegram_webdav.py

REM Небольшая пауза для запуска сервера
timeout /t 5 /nobreak >nul

REM Запуск монтирования диска в фоне (если rclone доступен)
rclone version >nul 2>&1
if not errorlevel 1 (
    echo 💾 Монтирование диска %DRIVE_LETTER%:...
    start "Telegram Mount" /min rclone mount telegram: %DRIVE_LETTER%: --vfs-cache-mode writes --vfs-cache-max-size 1G --dir-cache-time 5m -v
    
    REM Ждем появления диска
    for /l %%i in (1,1,10) do (
        if exist "%DRIVE_LETTER%:\" (
            echo ✅ Диск %DRIVE_LETTER%: готов!
            goto :disk_ready
        )
        timeout /t 1 /nobreak >nul
    )
    echo ⚠️  Диск монтируется в фоне...
    :disk_ready
) else (
    echo ⚠️  rclone не найден - только WebDAV режим
)

echo.
echo ✅ ГОТОВО!
echo.
echo 📁 Диск: %DRIVE_LETTER%:\
echo 🌐 WebDAV: http://localhost:8080
echo 💻 Веб-интерфейс: start python run.py
echo.

REM Быстрые действия
set /p action="Что сделать? [1-Открыть диск, 2-Открыть WebDAV, 3-Запустить веб-интерфейс, Enter-Продолжить]: "

if "%action%"=="1" (
    if exist "%DRIVE_LETTER%:\" (
        explorer %DRIVE_LETTER%:\
    ) else (
        echo Диск еще не готов
    )
)

if "%action%"=="2" (
    start http://localhost:8080
)

if "%action%"=="3" (
    start "Telegram Web Interface" python run.py
)

echo.
echo 🔄 Работает в фоне...
echo ⚠️  Не закрывайте это окно для продолжения работы
echo    Для остановки нажмите Ctrl+C
echo.

REM Минимальный мониторинг
:monitor_loop
timeout /t 30 /nobreak >nul
echo ⏰ %time% - Сервисы работают
goto :monitor_loop 