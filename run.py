#!/usr/bin/env python3
"""
run.py - Единый скрипт запуска TelegramCloudBasic
"""

import subprocess
import sys
import time
import signal
import os
from pathlib import Path
from threading import Thread
import argparse

class TelegramCloudRunner:
    """Класс для управления запуском всех компонентов."""
    
    def __init__(self):
        self.processes = {}
        self.running = True
        
    def run_component(self, name, cmd, cwd=None):
        """Запустить компонент в отдельном процессе."""
        print(f"🚀 Запуск {name}...")
        try:
            env = os.environ.copy()
            if Path("config.env").exists():
                # Загружаем переменные из config.env
                with open("config.env", 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            env[key] = value
            
            process = subprocess.Popen(
                cmd, shell=True, cwd=cwd, env=env,
                stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                universal_newlines=True, bufsize=1
            )
            self.processes[name] = process
            
            # Читаем вывод в отдельном потоке
            def read_output():
                while self.running and process.poll() is None:
                    line = process.stdout.readline()
                    if line:
                        print(f"[{name}] {line.rstrip()}")
                    else:
                        time.sleep(0.1)
            
            Thread(target=read_output, daemon=True).start()
            
            print(f"✅ {name} запущен (PID: {process.pid})")
            return process
            
        except Exception as e:
            print(f"❌ Ошибка запуска {name}: {e}")
            return None
    
    def stop_all(self):
        """Остановить все процессы."""
        print("\n🛑 Остановка всех компонентов...")
        self.running = False
        
        for name, process in self.processes.items():
            if process and process.poll() is None:
                print(f"🔸 Остановка {name}...")
                try:
                    if os.name == 'nt':
                        process.terminate()
                    else:
                        process.send_signal(signal.SIGTERM)
                    
                    # Ждём 5 секунд
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        print(f"⚡ Принудительная остановка {name}...")
                        process.kill()
                        
                except Exception as e:
                    print(f"❌ Ошибка остановки {name}: {e}")
        
        print("✅ Все компоненты остановлены")
    
    def wait_for_processes(self):
        """Ждать завершения процессов."""
        try:
            while self.running:
                # Проверяем статус процессов
                for name, process in list(self.processes.items()):
                    if process.poll() is not None:
                        print(f"⚠️ Процесс {name} завершился с кодом {process.returncode}")
                        if process.returncode != 0:
                            print(f"❌ {name} завершился с ошибкой")
                        del self.processes[name]
                
                if not self.processes:
                    print("🔚 Все процессы завершены")
                    break
                    
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⌨️ Получен сигнал остановки")
        finally:
            self.stop_all()

def check_config():
    """Проверить наличие конфигурации."""
    config_file = Path("config.env")
    if not config_file.exists():
        print("❌ Файл config.env не найден!")
        print("📝 Скопируйте config_example.env в config.env и настройте:")
        print("   cp config_example.env config.env")
        print("   # Отредактируйте config.env")
        return False
    
    # Загружаем переменные
    with open(config_file, 'r', encoding='utf-8') as f:
        env_vars = {}
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    # Проверяем тип конфигурации: бот или user API
    has_bot_token = env_vars.get('TG_BOT_TOKEN', '').strip()
    has_user_api = env_vars.get('TG_API_HASH', '').strip() and env_vars.get('TG_API_ID', '').strip()
    
    if has_bot_token:
        print("✅ Конфигурация бота найдена (безопасно)")
        return True
    elif has_user_api:
        if env_vars.get('TG_API_HASH') not in ['abcdef0123456789abcdef0123456789', '']:
            print("⚠️  Обнаружен User API (может конфликтовать с основным Telegram)")
            print("🤖 Рекомендуется настроить бота: python bot_setup_guide.py")
            choice = input("Продолжить с User API? (y/N): ").strip().lower()
            if choice not in ['y', 'yes', 'д', 'да']:
                return False
            return True
    
    # Проверяем основные параметры
    required_vars = ['TG_API_ID', 'TG_API_HASH']
    missing = []
    
    for var in required_vars:
        if var not in env_vars or not env_vars[var] or env_vars[var] in ['123456', 'abcdef0123456789abcdef0123456789']:
            missing.append(var)
    
    if missing:
        print(f"❌ Не настроены переменные: {', '.join(missing)}")
        print("📝 Отредактируйте config.env или используйте: python bot_setup_guide.py")
        return False
    
    print("✅ Конфигурация найдена")
    return True

def main():
    """Главная функция."""
    parser = argparse.ArgumentParser(description="Запуск TelegramCloudBasic")
    parser.add_argument("--webdav-only", action="store_true", 
                       help="Запустить только WebDAV сервер")
    parser.add_argument("--web-only", action="store_true", 
                       help="Запустить только веб-интерфейс") 
    parser.add_argument("--bot-only", action="store_true", 
                       help="Запустить только бота")
    parser.add_argument("--no-web", action="store_true",
                       help="Не запускать веб-интерфейс")
    
    args = parser.parse_args()
    
    print("🚀 TelegramCloudBasic Runner")
    print("=" * 40)
    
    # Проверяем конфигурацию
    if not check_config():
        sys.exit(1)
    
    # Проверяем виртуальное окружение
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Виртуальное окружение активно")
    else:
        print("⚠️ Виртуальное окружение не активировано")
        print("📝 Рекомендуется активировать:")
        if os.name == 'nt':
            print("   venv\\Scripts\\activate")
        else:
            print("   source venv/bin/activate")
    
    runner = TelegramCloudRunner()
    
    # Определяем что запускать
    if args.webdav_only:
        components = [("WebDAV", "python telegram_webdav.py")]
    elif args.web_only:
        components = [("Web", "uvicorn api.main:app --host 127.0.0.1 --port 8000")]
    elif args.bot_only:
        components = [("Bot", "python bot/main.py")]
    else:
        # Запускаем все компоненты
        components = [
            ("WebDAV", "python telegram_webdav.py"),
        ]
        
        if not args.no_web:
            components.extend([
                ("Web", "uvicorn api.main:app --host 127.0.0.1 --port 8000"),
                ("Bot", "python bot/main.py")
            ])
    
    try:
        # Устанавливаем обработчик сигналов
        def signal_handler(signum, frame):
            runner.stop_all()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)
        
        # Запускаем компоненты
        for name, cmd in components:
            process = runner.run_component(name, cmd)
            if not process:
                print(f"❌ Не удалось запустить {name}")
                runner.stop_all()
                sys.exit(1)
            
            # Небольшая пауза между запусками
            time.sleep(2)
        
        print(f"\n🎉 Запущено компонентов: {len(components)}")
        print("🌐 Доступ:")
        
        if any("WebDAV" in comp[0] for comp in components):
            print("   WebDAV: http://localhost:8080")
        if any("Web" in comp[0] for comp in components):
            print("   Веб-интерфейс: http://localhost:8000")
        
        print("\n🛑 Для остановки нажмите Ctrl+C")
        
        # Ждём завершения
        runner.wait_for_processes()
        
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")
        runner.stop_all()
        sys.exit(1)

if __name__ == "__main__":
    main() 