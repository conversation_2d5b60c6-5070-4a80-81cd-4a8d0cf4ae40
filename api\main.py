from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse
import sqlite3
import os
import aiohttp
import json
from typing import List, Dict
import logging
import time
import asyncio
from dotenv import load_dotenv
import io

app = FastAPI(title="TelegramCloudBasic API")

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Загружаем переменные окружения
load_dotenv("config.env")

# Путь к базе данных SQLite
DB_PATH = "db/files.db"

# Telegram Bot API настройки из переменных окружения
BOT_TOKEN = os.getenv("TG_BOT_TOKEN")
CHANNEL_ID = os.getenv("TG_DEST_CHAT")

def get_db_connection():
    conn = sqlite3.connect(DB_PATH)
    return conn

@app.on_event("startup")
async def startup_db_client():
    # Проверяем наличие токена бота
    if not BOT_TOKEN:
        logger.error("TG_BOT_TOKEN не найден в config.env!")
        raise ValueError("TG_BOT_TOKEN не найден в config.env!")

    if not CHANNEL_ID:
        logger.error("TG_DEST_CHAT не найден в config.env!")
        raise ValueError("TG_DEST_CHAT не найден в config.env!")

    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_name TEXT NOT NULL,
            file_id TEXT NOT NULL,
            upload_date TEXT NOT NULL
        )
    ''')
    conn.commit()
    conn.close()
    logger.info("База данных инициализирована.")

@app.get("/files", response_model=List[Dict])
async def get_files():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT id, file_name, upload_date FROM files ORDER BY upload_date DESC")
    files = cursor.fetchall()
    conn.close()
    
    return [{"id": f[0], "file_name": f[1], "upload_date": f[2]} for f in files]

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    # Начинаем измерение времени
    start_time = time.time()
    
    # Читаем содержимое файла
    content = await file.read()
    file_name = file.filename
    file_size = len(content)  # Размер файла в байтах
    
    try:
        # Отправляем файл в Telegram через Bot API
        async with aiohttp.ClientSession() as session:
            form = aiohttp.FormData()
            form.add_field('chat_id', CHANNEL_ID)
            form.add_field('document', content, filename=file_name)
            
            async with session.post(f"https://api.telegram.org/bot{BOT_TOKEN}/sendDocument", data=form) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Ошибка при загрузке файла в Telegram: {error_text}")
                    raise HTTPException(status_code=500, detail=f"Ошибка Telegram API: {error_text}")
                
                result = await response.json()
                if not result.get("ok"):
                    logger.error(f"Ошибка Telegram API: {result}")
                    raise HTTPException(status_code=500, detail=f"Ошибка Telegram API: {result}")
                
                file_id = result["result"]["document"]["file_id"]
                upload_date = result["result"]["date"]
                from datetime import datetime
                upload_date_str = datetime.fromtimestamp(upload_date).strftime("%Y-%m-%d %H:%M:%S")
        
        # Заканчиваем измерение времени
        end_time = time.time()
        upload_duration = end_time - start_time
        
        # Вычисляем скорость загрузки
        if upload_duration > 0:
            speed_bps = file_size / upload_duration  # байт в секунду
            speed_kbps = speed_bps / 1024  # килобайт в секунду
            speed_mbps = speed_kbps / 1024  # мегабайт в секунду
        else:
            speed_bps = speed_kbps = speed_mbps = 0
        
        # Сохраняем метаданные в базу данных
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("INSERT INTO files (file_name, file_id, upload_date) VALUES (?, ?, ?)", (file_name, file_id, upload_date_str))
        conn.commit()
        file_db_id = cursor.lastrowid
        conn.close()
        
        # Логируем статистику
        size_mb = file_size / (1024 * 1024)
        logger.info(f"Файл загружен через API: {file_name}, размер: {size_mb:.2f} МБ, время: {upload_duration:.2f}с, скорость: {speed_mbps:.2f} МБ/с")
        
        return {
            "id": file_db_id, 
            "file_name": file_name, 
            "upload_date": upload_date_str,
            "file_size_mb": round(size_mb, 2),
            "upload_duration_seconds": round(upload_duration, 2),
            "upload_speed_mbps": round(speed_mbps, 2),
            "upload_speed_kbps": round(speed_kbps, 2)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка при загрузке файла: {e}")
        raise HTTPException(status_code=500, detail=f"Ошибка при загрузке файла: {str(e)}")

@app.get("/download/{file_id}")
async def download_file(file_id: int):
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT file_name, file_id FROM files WHERE id = ?", (file_id,))
    file_data = cursor.fetchone()
    conn.close()
    
    if not file_data:
        raise HTTPException(status_code=404, detail=f"Файл с ID {file_id} не найден")
    
    file_name, telegram_file_id = file_data
    
    # Получаем файл из Telegram
    async with aiohttp.ClientSession() as session:
        async with session.get(f"https://api.telegram.org/bot{BOT_TOKEN}/getFile?file_id={telegram_file_id}") as response:
            if response.status != 200:
                error_text = await response.text()
                logger.error(f"Ошибка при получении информации о файле: {error_text}")
                raise HTTPException(status_code=500, detail=f"Ошибка Telegram API: {error_text}")
            
            result = await response.json()
            if not result.get("ok"):
                logger.error(f"Ошибка Telegram API: {result}")
                raise HTTPException(status_code=500, detail=f"Ошибка Telegram API: {result}")
            
            file_path = result["result"]["file_path"]
            download_url = f"https://api.telegram.org/file/bot{BOT_TOKEN}/{file_path}"
        
        async with session.get(download_url) as file_response:
            if file_response.status != 200:
                error_text = await file_response.text()
                logger.error(f"Ошибка при скачивании файла: {error_text}")
                raise HTTPException(status_code=500, detail=f"Ошибка скачивания файла: {error_text}")

            content = await file_response.read()

            # Создаем поток для возврата файла
            file_stream = io.BytesIO(content)

            return StreamingResponse(
                io.BytesIO(content),
                media_type="application/octet-stream",
                headers={"Content-Disposition": f"attachment; filename={file_name}"}
            )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
