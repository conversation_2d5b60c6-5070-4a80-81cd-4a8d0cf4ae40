# План реализации TelegramCloudBasic

Это упрощенный план создания Telegram-бота для использования Telegram как облачного диска с базовым веб-интерфейсом. План сосредоточен на минимальной функциональности для быстрого создания рабочего прототипа.

## Цели проекта
1. Создать Telegram-бота для загрузки и хранения файлов от одного пользователя.
2. Реализовать базовый список файлов и возможность их скачивания.
3. Обеспечить минимальную организацию файлов (без сложных папок и тегов).
4. Предоставить простой веб-интерфейс для доступа к файлам.

## Архитектура
- **Telegram-бот**: Использует библиотеку `python-telegram-bot` для взаимодействия с Telegram API. Бот принимает файлы и сохраняет их в приватный канал.
- **База данных**: Локальная SQLite для хранения метаданных файлов (ID, имя, дата загрузки).
- **API-сервер**: Использует `FastAPI` для предоставления базового REST API (список файлов, загрузка, скачивание).
- **Веб-интерфейс**: Простая HTML-страница с минимальным JavaScript для отображения списка файлов и загрузки новых.

## Пошаговая реализация

### Этап 1: Подготовка окружения
1. Установить Python 3.9 или новее.
2. Создать виртуальное окружение (`python -m venv venv`) и активировать его.
3. Установить минимальный набор библиотек: `pip install python-telegram-bot fastapi uvicorn aiohttp`.
4. Зарегистрировать бота через BotFather в Telegram и получить токен.

### Этап 2: Создание структуры проекта
1. Создать директорию проекта `TelegramCloudBasic`.
2. Создать поддиректории: `bot` (для бота), `api` (для API-сервера), `web` (для веб-интерфейса), `db` (для базы данных).

### Этап 3: Разработка Telegram-бота
1. Создать файл `bot/main.py` для кода бота.
2. Реализовать базовые команды:
   - `/start` — приветственное сообщение и проверка user ID (бот работает только с одним пользователем).
   - `/upload` — загрузка файла в приватный канал Telegram.
   - `/list` — отображение списка загруженных файлов.
   - `/download <file_id>` — скачивание файла по ID.
3. Сохранять файлы в приватный канал (создать канал заранее и добавить бота как администратора).
4. Записывать метаданные файлов (ID, имя, дата) в базу данных SQLite.

### Этап 4: Настройка базы данных
1. Создать файл `db/schema.py` для структуры базы данных.
2. Использовать SQLite с одной таблицей `files` (ID, имя, дата загрузки, Telegram file ID).

### Этап 5: Разработка API-сервера
1. Создать файл `api/main.py` для FastAPI-сервера.
2. Реализовать минимальные эндпоинты:
   - `POST /upload` — загрузка файла через бота в Telegram.
   - `GET /files` — получение списка файлов.
   - `GET /download/{file_id}` — скачивание файла по ID.
3. Настроить запуск API-сервера локально без HTTPS (для упрощения).

### Этап 6: Создание простого веб-интерфейса
1. Создать папку `web` с файлом `index.html`.
2. Реализовать базовый интерфейс:
   - Список файлов с возможностью скачивания.
   - Форма для загрузки новых файлов.
3. Подключить интерфейс к API с помощью простого JavaScript (без фреймворков).

### Этап 7: Тестирование
1. Проверить загрузку файлов (до 2 ГБ).
2. Убедиться, что список файлов отображается корректно в веб-интерфейсе.
3. Проверить скачивание файлов через веб-интерфейс и бота.

### Этап 8: Документация и запуск
1. Создать простой файл `README.md` с инструкциями по запуску бота и API-сервера.
2. Настроить запуск бота и API через отдельные команды в терминале.

## Ограничения
- Нет сложной системы папок и тегов (только список файлов).
- Отсутствует автоматическая загрузка файлов с устройства.
- Нет интеграции с облачными сервисами.
- Минимальная безопасность (без HTTPS и сложной аутентификации).
- Telegram может ограничивать бота или удалять файлы при неактивности аккаунта.
