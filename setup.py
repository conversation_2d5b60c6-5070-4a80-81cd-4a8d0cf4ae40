#!/usr/bin/env python3
"""
setup.py - Автоматическая установка и настройка TelegramCloudBasic
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, check=True):
    """Выполнить команду в shell."""
    print(f"$ {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if check and result.returncode != 0:
        print(f"ОШИБКА: {result.stderr}")
        sys.exit(1)
    return result

def setup_virtualenv():
    """Создать и активировать виртуальное окружение."""
    print("🔧 Создание виртуального окружения...")
    
    if not Path("venv").exists():
        run_command(f"{sys.executable} -m venv venv")
    
    # Определяем путь к активации в зависимости от ОС
    if os.name == 'nt':  # Windows
        activate_script = "venv\\Scripts\\activate"
        python_path = "venv\\Scripts\\python"
        pip_path = "venv\\Scripts\\pip"
    else:  # Unix/Linux/MacOS
        activate_script = "venv/bin/activate"
        python_path = "venv/bin/python"
        pip_path = "venv/bin/pip"
    
    print(f"✅ Виртуальное окружение создано. Активируйте его:")
    if os.name == 'nt':
        print(f"   venv\\Scripts\\activate")
    else:
        print(f"   source venv/bin/activate")
    
    return python_path, pip_path

def install_dependencies(pip_path):
    """Установить зависимости."""
    print("📦 Установка зависимостей...")
    run_command(f"{pip_path} install -r requirements.txt")
    print("✅ Зависимости установлены")

def setup_config():
    """Настройка конфигурации."""
    print("⚙️ Настройка конфигурации...")
    
    config_file = "config.env"
    if not Path(config_file).exists():
        shutil.copy("config_example.env", config_file)
        print(f"✅ Создан файл {config_file}")
        print("📝 ВНИМАНИЕ: Отредактируйте config.env с вашими настройками!")
    else:
        print(f"⚠️ Файл {config_file} уже существует")

def check_rclone():
    """Проверить установку rclone."""
    print("🔍 Проверка rclone...")
    result = run_command("rclone version", check=False)
    if result.returncode == 0:
        print("✅ rclone установлен")
        return True
    else:
        print("❌ rclone не найден")
        print("📥 Установите rclone:")
        if os.name == 'nt':
            print("   Скачайте с https://rclone.org/downloads/")
        else:
            print("   curl https://rclone.org/install.sh | sudo bash")
        return False

def create_systemd_service():
    """Создать systemd service файл (только для Linux)."""
    if os.name == 'nt':
        return
    
    print("🔧 Создание systemd service...")
    
    current_dir = Path.cwd().absolute()
    python_path = current_dir / "venv" / "bin" / "python"
    
    service_content = f"""[Unit]
Description=Telegram WebDAV Gateway
After=network.target

[Service]
Type=simple
User={os.getlogin()}
WorkingDirectory={current_dir}
Environment=PATH={current_dir}/venv/bin
EnvironmentFile={current_dir}/config.env
ExecStart={python_path} telegram_webdav.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_file = Path("telegram-webdav.service")
    service_file.write_text(service_content)
    
    print(f"✅ Создан файл {service_file}")
    print("📝 Для автозапуска выполните:")
    print(f"   sudo cp {service_file} /etc/systemd/system/")
    print("   sudo systemctl enable telegram-webdav")
    print("   sudo systemctl start telegram-webdav")

def create_windows_batch():
    """Создать batch файлы для Windows."""
    if os.name != 'nt':
        return
    
    print("🔧 Создание batch файлов для Windows...")
    
    # Файл запуска WebDAV
    start_webdav = """@echo off
call venv\\Scripts\\activate
python telegram_webdav.py
pause
"""
    Path("start_webdav.bat").write_text(start_webdav)
    
    # Файл запуска веб-интерфейса
    start_web = """@echo off
call venv\\Scripts\\activate
uvicorn api.main:app --host 127.0.0.1 --port 8000
pause
"""
    Path("start_web.bat").write_text(start_web)
    
    print("✅ Созданы файлы:")
    print("   start_webdav.bat - запуск WebDAV сервера")
    print("   start_web.bat - запуск веб-интерфейса")

def main():
    """Основная функция установки."""
    print("🚀 Установка TelegramCloudBasic")
    print("=" * 40)
    
    # Проверка Python версии
    if sys.version_info < (3, 8):
        print("❌ Требуется Python 3.8 или новее")
        sys.exit(1)
    
    try:
        python_path, pip_path = setup_virtualenv()
        install_dependencies(pip_path)
        setup_config()
        check_rclone()
        
        if os.name == 'nt':
            create_windows_batch()
        else:
            create_systemd_service()
        
        print("\n🎉 Установка завершена!")
        print("\n📋 Следующие шаги:")
        print("1. Отредактируйте config.env с вашими API ключами")
        print("2. Активируйте виртуальное окружение:")
        if os.name == 'nt':
            print("   venv\\Scripts\\activate")
            print("3. Запустите WebDAV: start_webdav.bat")
            print("4. Запустите веб-интерфейс: start_web.bat")
        else:
            print("   source venv/bin/activate")
            print("3. Запустите WebDAV: python telegram_webdav.py")
            print("4. Запустите веб-интерфейс: uvicorn api.main:app")
        print("5. Настройте rclone: rclone config")
        
    except KeyboardInterrupt:
        print("\n❌ Установка прервана")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Ошибка: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 