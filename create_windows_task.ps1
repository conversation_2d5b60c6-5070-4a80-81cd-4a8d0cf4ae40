# Создание задачи в планировщике Windows для автозапуска Telegram WebDAV
# PowerShell скрипт для Windows 11

Write-Host "🚀 Создание задачи автозапуска Telegram WebDAV" -ForegroundColor Green
Write-Host "=" * 50

# Получаем пути
$PSScriptRoot = Split-Path -Parent $MyInvocation.MyCommand.Definition
$PythonExe = Join-Path $PSScriptRoot "venv\Scripts\python.exe"
$WebDAVScript = Join-Path $PSScriptRoot "telegram_webdav.py"

Write-Host "📁 Папка проекта: $PSScriptRoot"
Write-Host "🐍 Python: $PythonExe"
Write-Host "📜 Скрипт: $WebDAVScript"

# Проверяем наличие файлов
if (-not (Test-Path $PythonExe)) {
    Write-Host "❌ Python не найден: $PythonExe" -ForegroundColor Red
    Write-Host "Запустите: python setup.py" -ForegroundColor Yellow
    Read-Host "Нажмите Enter для выхода"
    exit 1
}

if (-not (Test-Path $WebDAVScript)) {
    Write-Host "❌ Скрипт WebDAV не найден: $WebDAVScript" -ForegroundColor Red
    Read-Host "Нажмите Enter для выхода"
    exit 1
}

try {
    # Удаляем существующую задачу если есть
    $existing = Get-ScheduledTask -TaskName "TelegramWebDAV" -ErrorAction SilentlyContinue
    if ($existing) {
        Write-Host "🗑️ Удаление существующей задачи..." -ForegroundColor Yellow
        Unregister-ScheduledTask -TaskName "TelegramWebDAV" -Confirm:$false
    }

    # Создаем действие (что запускать)
    $Action = New-ScheduledTaskAction -Execute $PythonExe -Argument "`"$WebDAVScript`"" -WorkingDirectory $PSScriptRoot

    # Создаем триггер (когда запускать) - при входе в систему
    $Trigger = New-ScheduledTaskTrigger -AtLogOn

    # Создаем настройки
    $Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -DontStopOnIdleEnd

    # Создаем основную информацию
    $Principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive

    # Регистрируем задачу
    Register-ScheduledTask -TaskName "TelegramWebDAV" -Action $Action -Trigger $Trigger -Settings $Settings -Principal $Principal -Description "Автозапуск Telegram WebDAV сервера"

    Write-Host "✅ Задача создана успешно!" -ForegroundColor Green
    Write-Host "📋 Название: TelegramWebDAV"
    Write-Host "⏰ Триггер: При входе в систему"
    Write-Host "🎯 Команда: $PythonExe `"$WebDAVScript`""
    Write-Host "📁 Рабочая папка: $PSScriptRoot"
    
    Write-Host ""
    Write-Host "💡 Управление задачей:" -ForegroundColor Cyan
    Write-Host "   - Просмотр: taskschd.msc -> Библиотека планировщика заданий"
    Write-Host "   - Запуск: Start-ScheduledTask -TaskName 'TelegramWebDAV'"
    Write-Host "   - Остановка: Stop-ScheduledTask -TaskName 'TelegramWebDAV'"
    Write-Host "   - Удаление: Unregister-ScheduledTask -TaskName 'TelegramWebDAV'"

} catch {
    Write-Host "❌ Ошибка создания задачи: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "Нажмите Enter для выхода" 