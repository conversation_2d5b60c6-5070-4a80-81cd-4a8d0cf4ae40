# 💾 Автомонтирование диска TelegramCloudBasic

## 🎯 Что это?

Автомонтирование позволяет подключить ваше Telegram облако как обычный диск Windows (например, диск T:). После подключения вы сможете работать с файлами в Telegram как с обычными файлами на компьютере.

## 🚀 Быстрый запуск

1. **Запустите основную систему:**
   ```
   python run.py
   ```
   Дождитесь сообщения "🎉 Запущено компонентов: 3"

2. **Запустите автомонтирование:**
   ```
   АВТОМОНТИРОВАНИЕ.bat
   ```
   или
   ```
   auto_mount.bat
   ```

3. **Готово!** Telegram облако будет доступно как диск T: (или другая свободная буква)

## 📂 Использование

После монтирования вы можете:

- Открыть диск T: в Проводнике Windows
- Копировать файлы перетаскиванием
- Создавать папки
- Редактировать файлы напрямую
- Использовать любые программы для работы с файлами

## ⚠️ Важные моменты

1. **Не закрывайте окно с автомонтированием** - это отключит диск
2. **WebDAV должен быть запущен** - сначала запустите `python run.py`
3. **Для отключения** нажмите Ctrl+C в окне автомонтирования
4. **Кэширование:** Файлы кэшируются локально для быстрого доступа (до 1GB)

## 🔧 Компоненты

Система использует:
- **rclone** - для монтирования WebDAV как диска
- **WinFsp** - драйвер файловой системы Windows
- **WebDAV сервер** - мост между rclone и Telegram

## 🛠️ Устранение проблем

### Диск не монтируется
1. Убедитесь, что WebDAV запущен (порт 8080)
2. Проверьте, что rclone.exe есть в папке проекта
3. Убедитесь, что WinFsp установлен

### Медленная работа
1. Увеличьте размер кэша в auto_mount.bat
2. Проверьте скорость интернета
3. Используйте SSD для кэша

### Файлы не синхронизируются
1. Подождите несколько секунд - синхронизация не мгновенная
2. Проверьте логи в окне автомонтирования
3. Перезапустите автомонтирование

## 📋 Команды для ручного управления

```bash
# Проверить статус диска
Get-PSDrive

# Размонтировать диск вручную
rclone.exe umount T:

# Монтировать с другими параметрами
rclone.exe mount telegram: T: --vfs-cache-mode writes -v
```

## 🎉 Готово!

Теперь ваше Telegram облако работает как обычный диск Windows!
