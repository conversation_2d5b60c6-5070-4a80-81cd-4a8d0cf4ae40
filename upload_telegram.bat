@echo off
REM Скрипт загрузки файла в Telegram облако

set RCLONE_PATH="C:/Program Files/rclone/rclone.exe"
set REMOTE_NAME=telegram

if "%1"=="" (
    echo Использование: %0 ^<файл_или_папка^>
    echo Пример: %0 "document.pdf"
    pause
    exit /b 1
)

set SOURCE=%1

echo 📤 Загрузка %SOURCE% в Telegram облако...

%RCLONE_PATH% copy "%SOURCE%" %REMOTE_NAME%:/ --progress

if %ERRORLEVEL% equ 0 (
    echo ✅ Загрузка завершена
) else (
    echo ❌ Ошибка загрузки
    pause
)
