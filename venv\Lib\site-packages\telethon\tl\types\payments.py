"""File generated by TLObjects' generator. All changes will be ERASED"""
from ...tl.tlobject import TLObject
from typing import Optional, List, Union, TYPE_CHECKING
import os
import struct
from datetime import datetime
if TYPE_CHECKING:
    from ...tl.types import TypeBankCardOpenUrl, TypeChat, TypeConnectedBotStarRef, TypeDataJSON, TypeInvoice, TypePaymentFormMethod, TypePaymentRequestedInfo, TypePaymentSavedCredentials, TypePeer, TypeSavedStarGift, TypeShippingOption, TypeStarGift, TypeStarGiftAttribute, TypeStarRefProgram, TypeStarsAmount, TypeStarsRevenueStatus, TypeStarsSubscription, TypeStarsTransaction, TypeStatsGraph, TypeUpdates, TypeUser, TypeWebDocument



class BankCardData(TLObject):
    CONSTRUCTOR_ID = 0x3e24e573
    SUBCLASS_OF_ID = 0x8c6dd68b

    def __init__(self, title: str, open_urls: List['TypeBankCardOpenUrl']):
        """
        Constructor for payments.BankCardData: Instance of BankCardData.
        """
        self.title = title
        self.open_urls = open_urls

    def to_dict(self):
        return {
            '_': 'BankCardData',
            'title': self.title,
            'open_urls': [] if self.open_urls is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.open_urls]
        }

    def _bytes(self):
        return b''.join((
            b's\xe5$>',
            self.serialize_bytes(self.title),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.open_urls)),b''.join(x._bytes() for x in self.open_urls),
        ))

    @classmethod
    def from_reader(cls, reader):
        _title = reader.tgread_string()
        reader.read_int()
        _open_urls = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _open_urls.append(_x)

        return cls(title=_title, open_urls=_open_urls)


class CheckedGiftCode(TLObject):
    CONSTRUCTOR_ID = 0x284a1096
    SUBCLASS_OF_ID = 0x5b2997e8

    def __init__(self, date: Optional[datetime], months: int, chats: List['TypeChat'], users: List['TypeUser'], via_giveaway: Optional[bool]=None, from_id: Optional['TypePeer']=None, giveaway_msg_id: Optional[int]=None, to_id: Optional[int]=None, used_date: Optional[datetime]=None):
        """
        Constructor for payments.CheckedGiftCode: Instance of CheckedGiftCode.
        """
        self.date = date
        self.months = months
        self.chats = chats
        self.users = users
        self.via_giveaway = via_giveaway
        self.from_id = from_id
        self.giveaway_msg_id = giveaway_msg_id
        self.to_id = to_id
        self.used_date = used_date

    def to_dict(self):
        return {
            '_': 'CheckedGiftCode',
            'date': self.date,
            'months': self.months,
            'chats': [] if self.chats is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.chats],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users],
            'via_giveaway': self.via_giveaway,
            'from_id': self.from_id.to_dict() if isinstance(self.from_id, TLObject) else self.from_id,
            'giveaway_msg_id': self.giveaway_msg_id,
            'to_id': self.to_id,
            'used_date': self.used_date
        }

    def _bytes(self):
        return b''.join((
            b'\x96\x10J(',
            struct.pack('<I', (0 if self.via_giveaway is None or self.via_giveaway is False else 4) | (0 if self.from_id is None or self.from_id is False else 16) | (0 if self.giveaway_msg_id is None or self.giveaway_msg_id is False else 8) | (0 if self.to_id is None or self.to_id is False else 1) | (0 if self.used_date is None or self.used_date is False else 2)),
            b'' if self.from_id is None or self.from_id is False else (self.from_id._bytes()),
            b'' if self.giveaway_msg_id is None or self.giveaway_msg_id is False else (struct.pack('<i', self.giveaway_msg_id)),
            b'' if self.to_id is None or self.to_id is False else (struct.pack('<q', self.to_id)),
            self.serialize_datetime(self.date),
            struct.pack('<i', self.months),
            b'' if self.used_date is None or self.used_date is False else (self.serialize_datetime(self.used_date)),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.chats)),b''.join(x._bytes() for x in self.chats),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _via_giveaway = bool(flags & 4)
        if flags & 16:
            _from_id = reader.tgread_object()
        else:
            _from_id = None
        if flags & 8:
            _giveaway_msg_id = reader.read_int()
        else:
            _giveaway_msg_id = None
        if flags & 1:
            _to_id = reader.read_long()
        else:
            _to_id = None
        _date = reader.tgread_date()
        _months = reader.read_int()
        if flags & 2:
            _used_date = reader.tgread_date()
        else:
            _used_date = None
        reader.read_int()
        _chats = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _chats.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(date=_date, months=_months, chats=_chats, users=_users, via_giveaway=_via_giveaway, from_id=_from_id, giveaway_msg_id=_giveaway_msg_id, to_id=_to_id, used_date=_used_date)


class ConnectedStarRefBots(TLObject):
    CONSTRUCTOR_ID = 0x98d5ea1d
    SUBCLASS_OF_ID = 0x235e1a67

    def __init__(self, count: int, connected_bots: List['TypeConnectedBotStarRef'], users: List['TypeUser']):
        """
        Constructor for payments.ConnectedStarRefBots: Instance of ConnectedStarRefBots.
        """
        self.count = count
        self.connected_bots = connected_bots
        self.users = users

    def to_dict(self):
        return {
            '_': 'ConnectedStarRefBots',
            'count': self.count,
            'connected_bots': [] if self.connected_bots is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.connected_bots],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users]
        }

    def _bytes(self):
        return b''.join((
            b'\x1d\xea\xd5\x98',
            struct.pack('<i', self.count),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.connected_bots)),b''.join(x._bytes() for x in self.connected_bots),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        _count = reader.read_int()
        reader.read_int()
        _connected_bots = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _connected_bots.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(count=_count, connected_bots=_connected_bots, users=_users)


class ExportedInvoice(TLObject):
    CONSTRUCTOR_ID = 0xaed0cbd9
    SUBCLASS_OF_ID = 0x36105432

    def __init__(self, url: str):
        """
        Constructor for payments.ExportedInvoice: Instance of ExportedInvoice.
        """
        self.url = url

    def to_dict(self):
        return {
            '_': 'ExportedInvoice',
            'url': self.url
        }

    def _bytes(self):
        return b''.join((
            b'\xd9\xcb\xd0\xae',
            self.serialize_bytes(self.url),
        ))

    @classmethod
    def from_reader(cls, reader):
        _url = reader.tgread_string()
        return cls(url=_url)


class GiveawayInfo(TLObject):
    CONSTRUCTOR_ID = 0x4367daa0
    SUBCLASS_OF_ID = 0x96a377bd

    def __init__(self, start_date: Optional[datetime], participating: Optional[bool]=None, preparing_results: Optional[bool]=None, joined_too_early_date: Optional[datetime]=None, admin_disallowed_chat_id: Optional[int]=None, disallowed_country: Optional[str]=None):
        """
        Constructor for payments.GiveawayInfo: Instance of either GiveawayInfo, GiveawayInfoResults.
        """
        self.start_date = start_date
        self.participating = participating
        self.preparing_results = preparing_results
        self.joined_too_early_date = joined_too_early_date
        self.admin_disallowed_chat_id = admin_disallowed_chat_id
        self.disallowed_country = disallowed_country

    def to_dict(self):
        return {
            '_': 'GiveawayInfo',
            'start_date': self.start_date,
            'participating': self.participating,
            'preparing_results': self.preparing_results,
            'joined_too_early_date': self.joined_too_early_date,
            'admin_disallowed_chat_id': self.admin_disallowed_chat_id,
            'disallowed_country': self.disallowed_country
        }

    def _bytes(self):
        return b''.join((
            b'\xa0\xdagC',
            struct.pack('<I', (0 if self.participating is None or self.participating is False else 1) | (0 if self.preparing_results is None or self.preparing_results is False else 8) | (0 if self.joined_too_early_date is None or self.joined_too_early_date is False else 2) | (0 if self.admin_disallowed_chat_id is None or self.admin_disallowed_chat_id is False else 4) | (0 if self.disallowed_country is None or self.disallowed_country is False else 16)),
            self.serialize_datetime(self.start_date),
            b'' if self.joined_too_early_date is None or self.joined_too_early_date is False else (self.serialize_datetime(self.joined_too_early_date)),
            b'' if self.admin_disallowed_chat_id is None or self.admin_disallowed_chat_id is False else (struct.pack('<q', self.admin_disallowed_chat_id)),
            b'' if self.disallowed_country is None or self.disallowed_country is False else (self.serialize_bytes(self.disallowed_country)),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _participating = bool(flags & 1)
        _preparing_results = bool(flags & 8)
        _start_date = reader.tgread_date()
        if flags & 2:
            _joined_too_early_date = reader.tgread_date()
        else:
            _joined_too_early_date = None
        if flags & 4:
            _admin_disallowed_chat_id = reader.read_long()
        else:
            _admin_disallowed_chat_id = None
        if flags & 16:
            _disallowed_country = reader.tgread_string()
        else:
            _disallowed_country = None
        return cls(start_date=_start_date, participating=_participating, preparing_results=_preparing_results, joined_too_early_date=_joined_too_early_date, admin_disallowed_chat_id=_admin_disallowed_chat_id, disallowed_country=_disallowed_country)


class GiveawayInfoResults(TLObject):
    CONSTRUCTOR_ID = 0xe175e66f
    SUBCLASS_OF_ID = 0x96a377bd

    def __init__(self, start_date: Optional[datetime], finish_date: Optional[datetime], winners_count: int, winner: Optional[bool]=None, refunded: Optional[bool]=None, gift_code_slug: Optional[str]=None, stars_prize: Optional[int]=None, activated_count: Optional[int]=None):
        """
        Constructor for payments.GiveawayInfo: Instance of either GiveawayInfo, GiveawayInfoResults.
        """
        self.start_date = start_date
        self.finish_date = finish_date
        self.winners_count = winners_count
        self.winner = winner
        self.refunded = refunded
        self.gift_code_slug = gift_code_slug
        self.stars_prize = stars_prize
        self.activated_count = activated_count

    def to_dict(self):
        return {
            '_': 'GiveawayInfoResults',
            'start_date': self.start_date,
            'finish_date': self.finish_date,
            'winners_count': self.winners_count,
            'winner': self.winner,
            'refunded': self.refunded,
            'gift_code_slug': self.gift_code_slug,
            'stars_prize': self.stars_prize,
            'activated_count': self.activated_count
        }

    def _bytes(self):
        return b''.join((
            b'o\xe6u\xe1',
            struct.pack('<I', (0 if self.winner is None or self.winner is False else 1) | (0 if self.refunded is None or self.refunded is False else 2) | (0 if self.gift_code_slug is None or self.gift_code_slug is False else 8) | (0 if self.stars_prize is None or self.stars_prize is False else 16) | (0 if self.activated_count is None or self.activated_count is False else 4)),
            self.serialize_datetime(self.start_date),
            b'' if self.gift_code_slug is None or self.gift_code_slug is False else (self.serialize_bytes(self.gift_code_slug)),
            b'' if self.stars_prize is None or self.stars_prize is False else (struct.pack('<q', self.stars_prize)),
            self.serialize_datetime(self.finish_date),
            struct.pack('<i', self.winners_count),
            b'' if self.activated_count is None or self.activated_count is False else (struct.pack('<i', self.activated_count)),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _winner = bool(flags & 1)
        _refunded = bool(flags & 2)
        _start_date = reader.tgread_date()
        if flags & 8:
            _gift_code_slug = reader.tgread_string()
        else:
            _gift_code_slug = None
        if flags & 16:
            _stars_prize = reader.read_long()
        else:
            _stars_prize = None
        _finish_date = reader.tgread_date()
        _winners_count = reader.read_int()
        if flags & 4:
            _activated_count = reader.read_int()
        else:
            _activated_count = None
        return cls(start_date=_start_date, finish_date=_finish_date, winners_count=_winners_count, winner=_winner, refunded=_refunded, gift_code_slug=_gift_code_slug, stars_prize=_stars_prize, activated_count=_activated_count)


class PaymentForm(TLObject):
    CONSTRUCTOR_ID = 0xa0058751
    SUBCLASS_OF_ID = 0xa0483f19

    def __init__(self, form_id: int, bot_id: int, title: str, description: str, invoice: 'TypeInvoice', provider_id: int, url: str, users: List['TypeUser'], can_save_credentials: Optional[bool]=None, password_missing: Optional[bool]=None, photo: Optional['TypeWebDocument']=None, native_provider: Optional[str]=None, native_params: Optional['TypeDataJSON']=None, additional_methods: Optional[List['TypePaymentFormMethod']]=None, saved_info: Optional['TypePaymentRequestedInfo']=None, saved_credentials: Optional[List['TypePaymentSavedCredentials']]=None):
        """
        Constructor for payments.PaymentForm: Instance of either PaymentForm, PaymentFormStars, PaymentFormStarGift.
        """
        self.form_id = form_id
        self.bot_id = bot_id
        self.title = title
        self.description = description
        self.invoice = invoice
        self.provider_id = provider_id
        self.url = url
        self.users = users
        self.can_save_credentials = can_save_credentials
        self.password_missing = password_missing
        self.photo = photo
        self.native_provider = native_provider
        self.native_params = native_params
        self.additional_methods = additional_methods
        self.saved_info = saved_info
        self.saved_credentials = saved_credentials

    def to_dict(self):
        return {
            '_': 'PaymentForm',
            'form_id': self.form_id,
            'bot_id': self.bot_id,
            'title': self.title,
            'description': self.description,
            'invoice': self.invoice.to_dict() if isinstance(self.invoice, TLObject) else self.invoice,
            'provider_id': self.provider_id,
            'url': self.url,
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users],
            'can_save_credentials': self.can_save_credentials,
            'password_missing': self.password_missing,
            'photo': self.photo.to_dict() if isinstance(self.photo, TLObject) else self.photo,
            'native_provider': self.native_provider,
            'native_params': self.native_params.to_dict() if isinstance(self.native_params, TLObject) else self.native_params,
            'additional_methods': [] if self.additional_methods is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.additional_methods],
            'saved_info': self.saved_info.to_dict() if isinstance(self.saved_info, TLObject) else self.saved_info,
            'saved_credentials': [] if self.saved_credentials is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.saved_credentials]
        }

    def _bytes(self):
        assert ((self.native_provider or self.native_provider is not None) and (self.native_params or self.native_params is not None)) or ((self.native_provider is None or self.native_provider is False) and (self.native_params is None or self.native_params is False)), 'native_provider, native_params parameters must all be False-y (like None) or all me True-y'
        return b''.join((
            b'Q\x87\x05\xa0',
            struct.pack('<I', (0 if self.can_save_credentials is None or self.can_save_credentials is False else 4) | (0 if self.password_missing is None or self.password_missing is False else 8) | (0 if self.photo is None or self.photo is False else 32) | (0 if self.native_provider is None or self.native_provider is False else 16) | (0 if self.native_params is None or self.native_params is False else 16) | (0 if self.additional_methods is None or self.additional_methods is False else 64) | (0 if self.saved_info is None or self.saved_info is False else 1) | (0 if self.saved_credentials is None or self.saved_credentials is False else 2)),
            struct.pack('<q', self.form_id),
            struct.pack('<q', self.bot_id),
            self.serialize_bytes(self.title),
            self.serialize_bytes(self.description),
            b'' if self.photo is None or self.photo is False else (self.photo._bytes()),
            self.invoice._bytes(),
            struct.pack('<q', self.provider_id),
            self.serialize_bytes(self.url),
            b'' if self.native_provider is None or self.native_provider is False else (self.serialize_bytes(self.native_provider)),
            b'' if self.native_params is None or self.native_params is False else (self.native_params._bytes()),
            b'' if self.additional_methods is None or self.additional_methods is False else b''.join((b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.additional_methods)),b''.join(x._bytes() for x in self.additional_methods))),
            b'' if self.saved_info is None or self.saved_info is False else (self.saved_info._bytes()),
            b'' if self.saved_credentials is None or self.saved_credentials is False else b''.join((b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.saved_credentials)),b''.join(x._bytes() for x in self.saved_credentials))),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _can_save_credentials = bool(flags & 4)
        _password_missing = bool(flags & 8)
        _form_id = reader.read_long()
        _bot_id = reader.read_long()
        _title = reader.tgread_string()
        _description = reader.tgread_string()
        if flags & 32:
            _photo = reader.tgread_object()
        else:
            _photo = None
        _invoice = reader.tgread_object()
        _provider_id = reader.read_long()
        _url = reader.tgread_string()
        if flags & 16:
            _native_provider = reader.tgread_string()
        else:
            _native_provider = None
        if flags & 16:
            _native_params = reader.tgread_object()
        else:
            _native_params = None
        if flags & 64:
            reader.read_int()
            _additional_methods = []
            for _ in range(reader.read_int()):
                _x = reader.tgread_object()
                _additional_methods.append(_x)

        else:
            _additional_methods = None
        if flags & 1:
            _saved_info = reader.tgread_object()
        else:
            _saved_info = None
        if flags & 2:
            reader.read_int()
            _saved_credentials = []
            for _ in range(reader.read_int()):
                _x = reader.tgread_object()
                _saved_credentials.append(_x)

        else:
            _saved_credentials = None
        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(form_id=_form_id, bot_id=_bot_id, title=_title, description=_description, invoice=_invoice, provider_id=_provider_id, url=_url, users=_users, can_save_credentials=_can_save_credentials, password_missing=_password_missing, photo=_photo, native_provider=_native_provider, native_params=_native_params, additional_methods=_additional_methods, saved_info=_saved_info, saved_credentials=_saved_credentials)


class PaymentFormStarGift(TLObject):
    CONSTRUCTOR_ID = 0xb425cfe1
    SUBCLASS_OF_ID = 0xa0483f19

    def __init__(self, form_id: int, invoice: 'TypeInvoice'):
        """
        Constructor for payments.PaymentForm: Instance of either PaymentForm, PaymentFormStars, PaymentFormStarGift.
        """
        self.form_id = form_id
        self.invoice = invoice

    def to_dict(self):
        return {
            '_': 'PaymentFormStarGift',
            'form_id': self.form_id,
            'invoice': self.invoice.to_dict() if isinstance(self.invoice, TLObject) else self.invoice
        }

    def _bytes(self):
        return b''.join((
            b'\xe1\xcf%\xb4',
            struct.pack('<q', self.form_id),
            self.invoice._bytes(),
        ))

    @classmethod
    def from_reader(cls, reader):
        _form_id = reader.read_long()
        _invoice = reader.tgread_object()
        return cls(form_id=_form_id, invoice=_invoice)


class PaymentFormStars(TLObject):
    CONSTRUCTOR_ID = 0x7bf6b15c
    SUBCLASS_OF_ID = 0xa0483f19

    def __init__(self, form_id: int, bot_id: int, title: str, description: str, invoice: 'TypeInvoice', users: List['TypeUser'], photo: Optional['TypeWebDocument']=None):
        """
        Constructor for payments.PaymentForm: Instance of either PaymentForm, PaymentFormStars, PaymentFormStarGift.
        """
        self.form_id = form_id
        self.bot_id = bot_id
        self.title = title
        self.description = description
        self.invoice = invoice
        self.users = users
        self.photo = photo

    def to_dict(self):
        return {
            '_': 'PaymentFormStars',
            'form_id': self.form_id,
            'bot_id': self.bot_id,
            'title': self.title,
            'description': self.description,
            'invoice': self.invoice.to_dict() if isinstance(self.invoice, TLObject) else self.invoice,
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users],
            'photo': self.photo.to_dict() if isinstance(self.photo, TLObject) else self.photo
        }

    def _bytes(self):
        return b''.join((
            b'\\\xb1\xf6{',
            struct.pack('<I', (0 if self.photo is None or self.photo is False else 32)),
            struct.pack('<q', self.form_id),
            struct.pack('<q', self.bot_id),
            self.serialize_bytes(self.title),
            self.serialize_bytes(self.description),
            b'' if self.photo is None or self.photo is False else (self.photo._bytes()),
            self.invoice._bytes(),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _form_id = reader.read_long()
        _bot_id = reader.read_long()
        _title = reader.tgread_string()
        _description = reader.tgread_string()
        if flags & 32:
            _photo = reader.tgread_object()
        else:
            _photo = None
        _invoice = reader.tgread_object()
        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(form_id=_form_id, bot_id=_bot_id, title=_title, description=_description, invoice=_invoice, users=_users, photo=_photo)


class PaymentReceipt(TLObject):
    CONSTRUCTOR_ID = 0x70c4fe03
    SUBCLASS_OF_ID = 0x590093c9

    def __init__(self, date: Optional[datetime], bot_id: int, provider_id: int, title: str, description: str, invoice: 'TypeInvoice', currency: str, total_amount: int, credentials_title: str, users: List['TypeUser'], photo: Optional['TypeWebDocument']=None, info: Optional['TypePaymentRequestedInfo']=None, shipping: Optional['TypeShippingOption']=None, tip_amount: Optional[int]=None):
        """
        Constructor for payments.PaymentReceipt: Instance of either PaymentReceipt, PaymentReceiptStars.
        """
        self.date = date
        self.bot_id = bot_id
        self.provider_id = provider_id
        self.title = title
        self.description = description
        self.invoice = invoice
        self.currency = currency
        self.total_amount = total_amount
        self.credentials_title = credentials_title
        self.users = users
        self.photo = photo
        self.info = info
        self.shipping = shipping
        self.tip_amount = tip_amount

    def to_dict(self):
        return {
            '_': 'PaymentReceipt',
            'date': self.date,
            'bot_id': self.bot_id,
            'provider_id': self.provider_id,
            'title': self.title,
            'description': self.description,
            'invoice': self.invoice.to_dict() if isinstance(self.invoice, TLObject) else self.invoice,
            'currency': self.currency,
            'total_amount': self.total_amount,
            'credentials_title': self.credentials_title,
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users],
            'photo': self.photo.to_dict() if isinstance(self.photo, TLObject) else self.photo,
            'info': self.info.to_dict() if isinstance(self.info, TLObject) else self.info,
            'shipping': self.shipping.to_dict() if isinstance(self.shipping, TLObject) else self.shipping,
            'tip_amount': self.tip_amount
        }

    def _bytes(self):
        return b''.join((
            b'\x03\xfe\xc4p',
            struct.pack('<I', (0 if self.photo is None or self.photo is False else 4) | (0 if self.info is None or self.info is False else 1) | (0 if self.shipping is None or self.shipping is False else 2) | (0 if self.tip_amount is None or self.tip_amount is False else 8)),
            self.serialize_datetime(self.date),
            struct.pack('<q', self.bot_id),
            struct.pack('<q', self.provider_id),
            self.serialize_bytes(self.title),
            self.serialize_bytes(self.description),
            b'' if self.photo is None or self.photo is False else (self.photo._bytes()),
            self.invoice._bytes(),
            b'' if self.info is None or self.info is False else (self.info._bytes()),
            b'' if self.shipping is None or self.shipping is False else (self.shipping._bytes()),
            b'' if self.tip_amount is None or self.tip_amount is False else (struct.pack('<q', self.tip_amount)),
            self.serialize_bytes(self.currency),
            struct.pack('<q', self.total_amount),
            self.serialize_bytes(self.credentials_title),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _date = reader.tgread_date()
        _bot_id = reader.read_long()
        _provider_id = reader.read_long()
        _title = reader.tgread_string()
        _description = reader.tgread_string()
        if flags & 4:
            _photo = reader.tgread_object()
        else:
            _photo = None
        _invoice = reader.tgread_object()
        if flags & 1:
            _info = reader.tgread_object()
        else:
            _info = None
        if flags & 2:
            _shipping = reader.tgread_object()
        else:
            _shipping = None
        if flags & 8:
            _tip_amount = reader.read_long()
        else:
            _tip_amount = None
        _currency = reader.tgread_string()
        _total_amount = reader.read_long()
        _credentials_title = reader.tgread_string()
        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(date=_date, bot_id=_bot_id, provider_id=_provider_id, title=_title, description=_description, invoice=_invoice, currency=_currency, total_amount=_total_amount, credentials_title=_credentials_title, users=_users, photo=_photo, info=_info, shipping=_shipping, tip_amount=_tip_amount)


class PaymentReceiptStars(TLObject):
    CONSTRUCTOR_ID = 0xdabbf83a
    SUBCLASS_OF_ID = 0x590093c9

    def __init__(self, date: Optional[datetime], bot_id: int, title: str, description: str, invoice: 'TypeInvoice', currency: str, total_amount: int, transaction_id: str, users: List['TypeUser'], photo: Optional['TypeWebDocument']=None):
        """
        Constructor for payments.PaymentReceipt: Instance of either PaymentReceipt, PaymentReceiptStars.
        """
        self.date = date
        self.bot_id = bot_id
        self.title = title
        self.description = description
        self.invoice = invoice
        self.currency = currency
        self.total_amount = total_amount
        self.transaction_id = transaction_id
        self.users = users
        self.photo = photo

    def to_dict(self):
        return {
            '_': 'PaymentReceiptStars',
            'date': self.date,
            'bot_id': self.bot_id,
            'title': self.title,
            'description': self.description,
            'invoice': self.invoice.to_dict() if isinstance(self.invoice, TLObject) else self.invoice,
            'currency': self.currency,
            'total_amount': self.total_amount,
            'transaction_id': self.transaction_id,
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users],
            'photo': self.photo.to_dict() if isinstance(self.photo, TLObject) else self.photo
        }

    def _bytes(self):
        return b''.join((
            b':\xf8\xbb\xda',
            struct.pack('<I', (0 if self.photo is None or self.photo is False else 4)),
            self.serialize_datetime(self.date),
            struct.pack('<q', self.bot_id),
            self.serialize_bytes(self.title),
            self.serialize_bytes(self.description),
            b'' if self.photo is None or self.photo is False else (self.photo._bytes()),
            self.invoice._bytes(),
            self.serialize_bytes(self.currency),
            struct.pack('<q', self.total_amount),
            self.serialize_bytes(self.transaction_id),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _date = reader.tgread_date()
        _bot_id = reader.read_long()
        _title = reader.tgread_string()
        _description = reader.tgread_string()
        if flags & 4:
            _photo = reader.tgread_object()
        else:
            _photo = None
        _invoice = reader.tgread_object()
        _currency = reader.tgread_string()
        _total_amount = reader.read_long()
        _transaction_id = reader.tgread_string()
        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(date=_date, bot_id=_bot_id, title=_title, description=_description, invoice=_invoice, currency=_currency, total_amount=_total_amount, transaction_id=_transaction_id, users=_users, photo=_photo)


class PaymentResult(TLObject):
    CONSTRUCTOR_ID = 0x4e5f810d
    SUBCLASS_OF_ID = 0x8ae16a9d

    def __init__(self, updates: 'TypeUpdates'):
        """
        Constructor for payments.PaymentResult: Instance of either PaymentResult, PaymentVerificationNeeded.
        """
        self.updates = updates

    def to_dict(self):
        return {
            '_': 'PaymentResult',
            'updates': self.updates.to_dict() if isinstance(self.updates, TLObject) else self.updates
        }

    def _bytes(self):
        return b''.join((
            b'\r\x81_N',
            self.updates._bytes(),
        ))

    @classmethod
    def from_reader(cls, reader):
        _updates = reader.tgread_object()
        return cls(updates=_updates)


class PaymentVerificationNeeded(TLObject):
    CONSTRUCTOR_ID = 0xd8411139
    SUBCLASS_OF_ID = 0x8ae16a9d

    def __init__(self, url: str):
        """
        Constructor for payments.PaymentResult: Instance of either PaymentResult, PaymentVerificationNeeded.
        """
        self.url = url

    def to_dict(self):
        return {
            '_': 'PaymentVerificationNeeded',
            'url': self.url
        }

    def _bytes(self):
        return b''.join((
            b'9\x11A\xd8',
            self.serialize_bytes(self.url),
        ))

    @classmethod
    def from_reader(cls, reader):
        _url = reader.tgread_string()
        return cls(url=_url)


class SavedInfo(TLObject):
    CONSTRUCTOR_ID = 0xfb8fe43c
    SUBCLASS_OF_ID = 0xad3cf146

    def __init__(self, has_saved_credentials: Optional[bool]=None, saved_info: Optional['TypePaymentRequestedInfo']=None):
        """
        Constructor for payments.SavedInfo: Instance of SavedInfo.
        """
        self.has_saved_credentials = has_saved_credentials
        self.saved_info = saved_info

    def to_dict(self):
        return {
            '_': 'SavedInfo',
            'has_saved_credentials': self.has_saved_credentials,
            'saved_info': self.saved_info.to_dict() if isinstance(self.saved_info, TLObject) else self.saved_info
        }

    def _bytes(self):
        return b''.join((
            b'<\xe4\x8f\xfb',
            struct.pack('<I', (0 if self.has_saved_credentials is None or self.has_saved_credentials is False else 2) | (0 if self.saved_info is None or self.saved_info is False else 1)),
            b'' if self.saved_info is None or self.saved_info is False else (self.saved_info._bytes()),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _has_saved_credentials = bool(flags & 2)
        if flags & 1:
            _saved_info = reader.tgread_object()
        else:
            _saved_info = None
        return cls(has_saved_credentials=_has_saved_credentials, saved_info=_saved_info)


class SavedStarGifts(TLObject):
    CONSTRUCTOR_ID = 0x95f389b1
    SUBCLASS_OF_ID = 0xd5112897

    def __init__(self, count: int, gifts: List['TypeSavedStarGift'], chats: List['TypeChat'], users: List['TypeUser'], chat_notifications_enabled: Optional[bool]=None, next_offset: Optional[str]=None):
        """
        Constructor for payments.SavedStarGifts: Instance of SavedStarGifts.
        """
        self.count = count
        self.gifts = gifts
        self.chats = chats
        self.users = users
        self.chat_notifications_enabled = chat_notifications_enabled
        self.next_offset = next_offset

    def to_dict(self):
        return {
            '_': 'SavedStarGifts',
            'count': self.count,
            'gifts': [] if self.gifts is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.gifts],
            'chats': [] if self.chats is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.chats],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users],
            'chat_notifications_enabled': self.chat_notifications_enabled,
            'next_offset': self.next_offset
        }

    def _bytes(self):
        return b''.join((
            b'\xb1\x89\xf3\x95',
            struct.pack('<I', (0 if self.chat_notifications_enabled is None else 2) | (0 if self.next_offset is None or self.next_offset is False else 1)),
            struct.pack('<i', self.count),
            b'' if self.chat_notifications_enabled is None else (b'\xb5ur\x99' if self.chat_notifications_enabled else b'7\x97y\xbc'),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.gifts)),b''.join(x._bytes() for x in self.gifts),
            b'' if self.next_offset is None or self.next_offset is False else (self.serialize_bytes(self.next_offset)),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.chats)),b''.join(x._bytes() for x in self.chats),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _count = reader.read_int()
        if flags & 2:
            _chat_notifications_enabled = reader.tgread_bool()
        else:
            _chat_notifications_enabled = None
        reader.read_int()
        _gifts = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _gifts.append(_x)

        if flags & 1:
            _next_offset = reader.tgread_string()
        else:
            _next_offset = None
        reader.read_int()
        _chats = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _chats.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(count=_count, gifts=_gifts, chats=_chats, users=_users, chat_notifications_enabled=_chat_notifications_enabled, next_offset=_next_offset)


class StarGiftUpgradePreview(TLObject):
    CONSTRUCTOR_ID = 0x167bd90b
    SUBCLASS_OF_ID = 0x5e2b68c7

    def __init__(self, sample_attributes: List['TypeStarGiftAttribute']):
        """
        Constructor for payments.StarGiftUpgradePreview: Instance of StarGiftUpgradePreview.
        """
        self.sample_attributes = sample_attributes

    def to_dict(self):
        return {
            '_': 'StarGiftUpgradePreview',
            'sample_attributes': [] if self.sample_attributes is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.sample_attributes]
        }

    def _bytes(self):
        return b''.join((
            b'\x0b\xd9{\x16',
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.sample_attributes)),b''.join(x._bytes() for x in self.sample_attributes),
        ))

    @classmethod
    def from_reader(cls, reader):
        reader.read_int()
        _sample_attributes = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _sample_attributes.append(_x)

        return cls(sample_attributes=_sample_attributes)


class StarGiftWithdrawalUrl(TLObject):
    CONSTRUCTOR_ID = 0x84aa3a9c
    SUBCLASS_OF_ID = 0xa2822dc5

    def __init__(self, url: str):
        """
        Constructor for payments.StarGiftWithdrawalUrl: Instance of StarGiftWithdrawalUrl.
        """
        self.url = url

    def to_dict(self):
        return {
            '_': 'StarGiftWithdrawalUrl',
            'url': self.url
        }

    def _bytes(self):
        return b''.join((
            b'\x9c:\xaa\x84',
            self.serialize_bytes(self.url),
        ))

    @classmethod
    def from_reader(cls, reader):
        _url = reader.tgread_string()
        return cls(url=_url)


class StarGifts(TLObject):
    CONSTRUCTOR_ID = 0x901689ea
    SUBCLASS_OF_ID = 0x6178d9a4

    def __init__(self, hash: int, gifts: List['TypeStarGift']):
        """
        Constructor for payments.StarGifts: Instance of either StarGiftsNotModified, StarGifts.
        """
        self.hash = hash
        self.gifts = gifts

    def to_dict(self):
        return {
            '_': 'StarGifts',
            'hash': self.hash,
            'gifts': [] if self.gifts is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.gifts]
        }

    def _bytes(self):
        return b''.join((
            b'\xea\x89\x16\x90',
            struct.pack('<i', self.hash),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.gifts)),b''.join(x._bytes() for x in self.gifts),
        ))

    @classmethod
    def from_reader(cls, reader):
        _hash = reader.read_int()
        reader.read_int()
        _gifts = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _gifts.append(_x)

        return cls(hash=_hash, gifts=_gifts)


class StarGiftsNotModified(TLObject):
    CONSTRUCTOR_ID = 0xa388a368
    SUBCLASS_OF_ID = 0x6178d9a4

    def to_dict(self):
        return {
            '_': 'StarGiftsNotModified'
        }

    def _bytes(self):
        return b''.join((
            b'h\xa3\x88\xa3',
        ))

    @classmethod
    def from_reader(cls, reader):
        return cls()


class StarsRevenueAdsAccountUrl(TLObject):
    CONSTRUCTOR_ID = 0x394e7f21
    SUBCLASS_OF_ID = 0x4a228b15

    def __init__(self, url: str):
        """
        Constructor for payments.StarsRevenueAdsAccountUrl: Instance of StarsRevenueAdsAccountUrl.
        """
        self.url = url

    def to_dict(self):
        return {
            '_': 'StarsRevenueAdsAccountUrl',
            'url': self.url
        }

    def _bytes(self):
        return b''.join((
            b'!\x7fN9',
            self.serialize_bytes(self.url),
        ))

    @classmethod
    def from_reader(cls, reader):
        _url = reader.tgread_string()
        return cls(url=_url)


class StarsRevenueStats(TLObject):
    CONSTRUCTOR_ID = 0xc92bb73b
    SUBCLASS_OF_ID = 0xa54755f3

    def __init__(self, revenue_graph: 'TypeStatsGraph', status: 'TypeStarsRevenueStatus', usd_rate: float):
        """
        Constructor for payments.StarsRevenueStats: Instance of StarsRevenueStats.
        """
        self.revenue_graph = revenue_graph
        self.status = status
        self.usd_rate = usd_rate

    def to_dict(self):
        return {
            '_': 'StarsRevenueStats',
            'revenue_graph': self.revenue_graph.to_dict() if isinstance(self.revenue_graph, TLObject) else self.revenue_graph,
            'status': self.status.to_dict() if isinstance(self.status, TLObject) else self.status,
            'usd_rate': self.usd_rate
        }

    def _bytes(self):
        return b''.join((
            b';\xb7+\xc9',
            self.revenue_graph._bytes(),
            self.status._bytes(),
            struct.pack('<d', self.usd_rate),
        ))

    @classmethod
    def from_reader(cls, reader):
        _revenue_graph = reader.tgread_object()
        _status = reader.tgread_object()
        _usd_rate = reader.read_double()
        return cls(revenue_graph=_revenue_graph, status=_status, usd_rate=_usd_rate)


class StarsRevenueWithdrawalUrl(TLObject):
    CONSTRUCTOR_ID = 0x1dab80b7
    SUBCLASS_OF_ID = 0x8466a0ee

    def __init__(self, url: str):
        """
        Constructor for payments.StarsRevenueWithdrawalUrl: Instance of StarsRevenueWithdrawalUrl.
        """
        self.url = url

    def to_dict(self):
        return {
            '_': 'StarsRevenueWithdrawalUrl',
            'url': self.url
        }

    def _bytes(self):
        return b''.join((
            b'\xb7\x80\xab\x1d',
            self.serialize_bytes(self.url),
        ))

    @classmethod
    def from_reader(cls, reader):
        _url = reader.tgread_string()
        return cls(url=_url)


class StarsStatus(TLObject):
    CONSTRUCTOR_ID = 0x6c9ce8ed
    SUBCLASS_OF_ID = 0x6e9c1d6f

    def __init__(self, balance: 'TypeStarsAmount', chats: List['TypeChat'], users: List['TypeUser'], subscriptions: Optional[List['TypeStarsSubscription']]=None, subscriptions_next_offset: Optional[str]=None, subscriptions_missing_balance: Optional[int]=None, history: Optional[List['TypeStarsTransaction']]=None, next_offset: Optional[str]=None):
        """
        Constructor for payments.StarsStatus: Instance of StarsStatus.
        """
        self.balance = balance
        self.chats = chats
        self.users = users
        self.subscriptions = subscriptions
        self.subscriptions_next_offset = subscriptions_next_offset
        self.subscriptions_missing_balance = subscriptions_missing_balance
        self.history = history
        self.next_offset = next_offset

    def to_dict(self):
        return {
            '_': 'StarsStatus',
            'balance': self.balance.to_dict() if isinstance(self.balance, TLObject) else self.balance,
            'chats': [] if self.chats is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.chats],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users],
            'subscriptions': [] if self.subscriptions is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.subscriptions],
            'subscriptions_next_offset': self.subscriptions_next_offset,
            'subscriptions_missing_balance': self.subscriptions_missing_balance,
            'history': [] if self.history is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.history],
            'next_offset': self.next_offset
        }

    def _bytes(self):
        return b''.join((
            b'\xed\xe8\x9cl',
            struct.pack('<I', (0 if self.subscriptions is None or self.subscriptions is False else 2) | (0 if self.subscriptions_next_offset is None or self.subscriptions_next_offset is False else 4) | (0 if self.subscriptions_missing_balance is None or self.subscriptions_missing_balance is False else 16) | (0 if self.history is None or self.history is False else 8) | (0 if self.next_offset is None or self.next_offset is False else 1)),
            self.balance._bytes(),
            b'' if self.subscriptions is None or self.subscriptions is False else b''.join((b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.subscriptions)),b''.join(x._bytes() for x in self.subscriptions))),
            b'' if self.subscriptions_next_offset is None or self.subscriptions_next_offset is False else (self.serialize_bytes(self.subscriptions_next_offset)),
            b'' if self.subscriptions_missing_balance is None or self.subscriptions_missing_balance is False else (struct.pack('<q', self.subscriptions_missing_balance)),
            b'' if self.history is None or self.history is False else b''.join((b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.history)),b''.join(x._bytes() for x in self.history))),
            b'' if self.next_offset is None or self.next_offset is False else (self.serialize_bytes(self.next_offset)),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.chats)),b''.join(x._bytes() for x in self.chats),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _balance = reader.tgread_object()
        if flags & 2:
            reader.read_int()
            _subscriptions = []
            for _ in range(reader.read_int()):
                _x = reader.tgread_object()
                _subscriptions.append(_x)

        else:
            _subscriptions = None
        if flags & 4:
            _subscriptions_next_offset = reader.tgread_string()
        else:
            _subscriptions_next_offset = None
        if flags & 16:
            _subscriptions_missing_balance = reader.read_long()
        else:
            _subscriptions_missing_balance = None
        if flags & 8:
            reader.read_int()
            _history = []
            for _ in range(reader.read_int()):
                _x = reader.tgread_object()
                _history.append(_x)

        else:
            _history = None
        if flags & 1:
            _next_offset = reader.tgread_string()
        else:
            _next_offset = None
        reader.read_int()
        _chats = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _chats.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(balance=_balance, chats=_chats, users=_users, subscriptions=_subscriptions, subscriptions_next_offset=_subscriptions_next_offset, subscriptions_missing_balance=_subscriptions_missing_balance, history=_history, next_offset=_next_offset)


class SuggestedStarRefBots(TLObject):
    CONSTRUCTOR_ID = 0xb4d5d859
    SUBCLASS_OF_ID = 0x70189243

    def __init__(self, count: int, suggested_bots: List['TypeStarRefProgram'], users: List['TypeUser'], next_offset: Optional[str]=None):
        """
        Constructor for payments.SuggestedStarRefBots: Instance of SuggestedStarRefBots.
        """
        self.count = count
        self.suggested_bots = suggested_bots
        self.users = users
        self.next_offset = next_offset

    def to_dict(self):
        return {
            '_': 'SuggestedStarRefBots',
            'count': self.count,
            'suggested_bots': [] if self.suggested_bots is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.suggested_bots],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users],
            'next_offset': self.next_offset
        }

    def _bytes(self):
        return b''.join((
            b'Y\xd8\xd5\xb4',
            struct.pack('<I', (0 if self.next_offset is None or self.next_offset is False else 1)),
            struct.pack('<i', self.count),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.suggested_bots)),b''.join(x._bytes() for x in self.suggested_bots),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
            b'' if self.next_offset is None or self.next_offset is False else (self.serialize_bytes(self.next_offset)),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _count = reader.read_int()
        reader.read_int()
        _suggested_bots = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _suggested_bots.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        if flags & 1:
            _next_offset = reader.tgread_string()
        else:
            _next_offset = None
        return cls(count=_count, suggested_bots=_suggested_bots, users=_users, next_offset=_next_offset)


class UniqueStarGift(TLObject):
    CONSTRUCTOR_ID = 0xcaa2f60b
    SUBCLASS_OF_ID = 0x78b0c5fb

    def __init__(self, gift: 'TypeStarGift', users: List['TypeUser']):
        """
        Constructor for payments.UniqueStarGift: Instance of UniqueStarGift.
        """
        self.gift = gift
        self.users = users

    def to_dict(self):
        return {
            '_': 'UniqueStarGift',
            'gift': self.gift.to_dict() if isinstance(self.gift, TLObject) else self.gift,
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users]
        }

    def _bytes(self):
        return b''.join((
            b'\x0b\xf6\xa2\xca',
            self.gift._bytes(),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        _gift = reader.tgread_object()
        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(gift=_gift, users=_users)


class ValidatedRequestedInfo(TLObject):
    CONSTRUCTOR_ID = 0xd1451883
    SUBCLASS_OF_ID = 0x8f8044b7

    def __init__(self, id: Optional[str]=None, shipping_options: Optional[List['TypeShippingOption']]=None):
        """
        Constructor for payments.ValidatedRequestedInfo: Instance of ValidatedRequestedInfo.
        """
        self.id = id
        self.shipping_options = shipping_options

    def to_dict(self):
        return {
            '_': 'ValidatedRequestedInfo',
            'id': self.id,
            'shipping_options': [] if self.shipping_options is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.shipping_options]
        }

    def _bytes(self):
        return b''.join((
            b'\x83\x18E\xd1',
            struct.pack('<I', (0 if self.id is None or self.id is False else 1) | (0 if self.shipping_options is None or self.shipping_options is False else 2)),
            b'' if self.id is None or self.id is False else (self.serialize_bytes(self.id)),
            b'' if self.shipping_options is None or self.shipping_options is False else b''.join((b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.shipping_options)),b''.join(x._bytes() for x in self.shipping_options))),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        if flags & 1:
            _id = reader.tgread_string()
        else:
            _id = None
        if flags & 2:
            reader.read_int()
            _shipping_options = []
            for _ in range(reader.read_int()):
                _x = reader.tgread_object()
                _shipping_options.append(_x)

        else:
            _shipping_options = None
        return cls(id=_id, shipping_options=_shipping_options)

