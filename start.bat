@echo off
chcp 65001 >nul
echo 🚀 Запуск TelegramCloudBasic
echo ========================================

cd /d "%~dp0"

if not exist "venv\Scripts\python.exe" (
    echo ❌ Виртуальное окружение не найдено!
    echo 📝 Сначала запустите setup.py для установки
    pause
    exit /b 1
)

if not exist "config.env" (
    echo ❌ Файл config.env не найден!
    echo 📝 Скопируйте config_example.env в config.env и настройте
    pause
    exit /b 1
)

echo ✅ Запуск системы...
venv\Scripts\python.exe run.py

echo.
echo 🔚 Система остановлена
pause
