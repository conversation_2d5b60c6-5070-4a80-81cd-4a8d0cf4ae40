<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TelegramCloudBasic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        h1 {
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .upload-section {
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .message {
            margin-top: 10px;
            padding: 10px;
            border: 1px solid #ccc;
            display: none;
        }
        .success {
            color: green;
            border-color: green;
        }
        .error {
            color: red;
            border-color: red;
        }
    </style>
</head>
<body>
    <h1>TelegramCloudBasic</h1>
    <p>Управляйте вашими файлами в Telegram через этот простой веб-интерфейс.</p>

    <div class="upload-section">
        <h2>Загрузить файл</h2>
        <input type="file" id="fileInput" />
        <button onclick="uploadFile()">Загрузить</button>
        <div id="uploadMessage" class="message"></div>
    </div>

    <h2>Список файлов</h2>
    <button onclick="refreshFiles()">Обновить список</button>
    <table id="filesTable">
        <thead>
            <tr>
                <th>ID</th>
                <th>Название файла</th>
                <th>Дата загрузки</th>
                <th>Действия</th>
            </tr>
        </thead>
        <tbody id="filesBody">
            <!-- Список файлов будет загружен сюда -->
        </tbody>
    </table>

    <script>
        const API_URL = 'http://localhost:8000';

        async function refreshFiles() {
            try {
                const response = await fetch(`${API_URL}/files`);
                if (!response.ok) {
                    throw new Error('Ошибка при получении списка файлов');
                }
                const files = await response.json();
                updateFilesTable(files);
            } catch (error) {
                showMessage('uploadMessage', 'Ошибка: ' + error.message, false);
            }
        }

        function updateFilesTable(files) {
            const tbody = document.getElementById('filesBody');
            tbody.innerHTML = '';
            if (files.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4">Нет загруженных файлов.</td></tr>';
                return;
            }
            files.forEach(file => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${file.id}</td>
                    <td>${file.file_name}</td>
                    <td>${file.upload_date}</td>
                    <td><button onclick="downloadFile(${file.id})">Скачать</button></td>
                `;
                tbody.appendChild(row);
            });
        }

        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            if (!fileInput.files[0]) {
                showMessage('uploadMessage', 'Пожалуйста, выберите файл для загрузки.', false);
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);

            try {
                const response = await fetch(`${API_URL}/upload`, {
                    method: 'POST',
                    body: formData
                });
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Ошибка при загрузке файла: ${errorText}`);
                }
                const result = await response.json();
                showMessage('uploadMessage', `Файл "${result.file_name}" успешно загружен. ID: ${result.id}`, true);
                fileInput.value = ''; // Очищаем поле ввода
                refreshFiles(); // Обновляем список файлов
            } catch (error) {
                showMessage('uploadMessage', 'Ошибка: ' + error.message, false);
            }
        }

        async function downloadFile(fileId) {
            try {
                window.location.href = `${API_URL}/download/${fileId}`;
            } catch (error) {
                showMessage('uploadMessage', 'Ошибка при скачивании файла: ' + error.message, false);
            }
        }

        function showMessage(elementId, message, isSuccess) {
            const messageElement = document.getElementById(elementId);
            messageElement.textContent = message;
            messageElement.className = isSuccess ? 'message success' : 'message error';
            messageElement.style.display = 'block';
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }

        // Загружаем список файлов при открытии страницы
        window.onload = refreshFiles;
    </script>
</body>
</html>
