@echo off
REM Скрипт синхронизации с Telegram облаком
REM Использование: sync_telegram.bat <локальная_папка>

set RCLONE_PATH="rclone"
set REMOTE_NAME=telegram

if "%1"=="" (
    echo Использование: %0 ^<локальная_папка^>
    echo Пример: %0 "C:\Users\<USER>\Documents\MyCloud"
    pause
    exit /b 1
)

set LOCAL_PATH=%1

echo 🔄 Синхронизация %LOCAL_PATH% с Telegram облаком...

REM Синхронизация (загрузка изменений)
%RCLONE_PATH% sync "%LOCAL_PATH%" %REMOTE_NAME%:/ --progress --transfers 1 --checkers 1

if %ERRORLEVEL% equ 0 (
    echo ✅ Синхронизация завершена успешно
) else (
    echo ❌ Ошибка синхронизации
    pause
)
