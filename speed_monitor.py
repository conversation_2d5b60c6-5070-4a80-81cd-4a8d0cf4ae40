import time
import os
import sqlite3
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List
import matplotlib.pyplot as plt
import numpy as np

class SpeedMonitor:
    def __init__(self, db_path="../db/files.db"):
        self.db_path = db_path
        self.stats_file = "upload_stats.json"
        
    def save_upload_stats(self, file_name: str, file_size: int, upload_duration: float, speed_mbps: float):
        """Сохраняет статистику загрузки в JSON файл"""
        stats = {
            "timestamp": datetime.now().isoformat(),
            "file_name": file_name,
            "file_size_mb": file_size / (1024 * 1024),
            "upload_duration_seconds": upload_duration,
            "speed_mbps": speed_mbps,
            "speed_kbps": speed_mbps * 1024,
            "speed_bps": speed_mbps * 1024 * 1024
        }
        
        # Загружаем существующие данные
        all_stats = []
        if os.path.exists(self.stats_file):
            try:
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    all_stats = json.load(f)
            except:
                all_stats = []
        
        # Добавляем новую статистику
        all_stats.append(stats)
        
        # Сохраняем обновленные данные
        with open(self.stats_file, 'w', encoding='utf-8') as f:
            json.dump(all_stats, f, ensure_ascii=False, indent=2)
    
    def get_speed_statistics(self, hours=24) -> Dict:
        """Получает статистику скорости за последние N часов"""
        if not os.path.exists(self.stats_file):
            return {"error": "Нет данных для анализа"}
        
        with open(self.stats_file, 'r', encoding='utf-8') as f:
            all_stats = json.load(f)
        
        # Фильтруем данные за последние часы
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_stats = []
        
        for stat in all_stats:
            stat_time = datetime.fromisoformat(stat['timestamp'])
            if stat_time >= cutoff_time:
                recent_stats.append(stat)
        
        if not recent_stats:
            return {"error": f"Нет данных за последние {hours} часов"}
        
        speeds = [stat['speed_mbps'] for stat in recent_stats]
        sizes = [stat['file_size_mb'] for stat in recent_stats]
        durations = [stat['upload_duration_seconds'] for stat in recent_stats]
        
        return {
            "период_часов": hours,
            "количество_загрузок": len(recent_stats),
            "средняя_скорость_мбс": round(np.mean(speeds), 2),
            "максимальная_скорость_мбс": round(max(speeds), 2),
            "минимальная_скорость_мбс": round(min(speeds), 2),
            "медиана_скорости_мбс": round(np.median(speeds), 2),
            "общий_объем_мб": round(sum(sizes), 2),
            "общее_время_загрузки_сек": round(sum(durations), 2),
            "средний_размер_файла_мб": round(np.mean(sizes), 2)
        }
    
    def create_speed_chart(self, hours=24, output_file="speed_chart.png"):
        """Создает график скорости загрузки"""
        if not os.path.exists(self.stats_file):
            print("Нет данных для создания графика")
            return
        
        with open(self.stats_file, 'r', encoding='utf-8') as f:
            all_stats = json.load(f)
        
        # Фильтруем данные за последние часы
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_stats = []
        
        for stat in all_stats:
            stat_time = datetime.fromisoformat(stat['timestamp'])
            if stat_time >= cutoff_time:
                recent_stats.append(stat)
        
        if not recent_stats:
            print(f"Нет данных за последние {hours} часов")
            return
        
        # Подготавливаем данные для графика
        times = [datetime.fromisoformat(stat['timestamp']) for stat in recent_stats]
        speeds = [stat['speed_mbps'] for stat in recent_stats]
        sizes = [stat['file_size_mb'] for stat in recent_stats]
        
        # Создаем график
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # График скорости
        ax1.plot(times, speeds, 'b-o', linewidth=2, markersize=4)
        ax1.set_title(f'Скорость загрузки файлов (последние {hours} часов)', fontsize=14)
        ax1.set_ylabel('Скорость (МБ/с)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # График размеров файлов
        ax2.bar(range(len(sizes)), sizes, alpha=0.7, color='green')
        ax2.set_title('Размеры загруженных файлов', fontsize=14)
        ax2.set_ylabel('Размер (МБ)', fontsize=12)
        ax2.set_xlabel('Порядок загрузки', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"График сохранен в файл: {output_file}")
    
    def get_real_time_speed(self, file_path: str, chunk_size: int = 1024*1024) -> Dict:
        """Измеряет скорость записи файла на диск в реальном времени"""
        if not os.path.exists(file_path):
            return {"error": "Файл не найден"}
        
        file_size = os.path.getsize(file_path)
        start_time = time.time()
        
        # Создаем временный файл для тестирования записи
        temp_file = f"temp_speed_test_{int(time.time())}.tmp"
        
        try:
            with open(file_path, 'rb') as source:
                with open(temp_file, 'wb') as target:
                    bytes_written = 0
                    while True:
                        chunk = source.read(chunk_size)
                        if not chunk:
                            break
                        target.write(chunk)
                        bytes_written += len(chunk)
            
            end_time = time.time()
            duration = end_time - start_time
            
            if duration > 0:
                speed_bps = file_size / duration
                speed_mbps = speed_bps / (1024 * 1024)
            else:
                speed_bps = speed_mbps = 0
            
            return {
                "файл": file_path,
                "размер_мб": round(file_size / (1024 * 1024), 2),
                "время_записи_сек": round(duration, 2),
                "скорость_записи_мбс": round(speed_mbps, 2),
                "скорость_записи_кбс": round(speed_mbps * 1024, 2)
            }
        
        finally:
            # Удаляем временный файл
            if os.path.exists(temp_file):
                os.remove(temp_file)

# Пример использования
if __name__ == "__main__":
    monitor = SpeedMonitor()
    
    # Пример сохранения статистики
    # monitor.save_upload_stats("test.jpg", 5*1024*1024, 2.5, 2.0)
    
    # Получение статистики
    stats = monitor.get_speed_statistics(24)
    print("Статистика за 24 часа:")
    print(json.dumps(stats, ensure_ascii=False, indent=2))
    
    # Создание графика (требует matplotlib)
    try:
        monitor.create_speed_chart(24)
    except ImportError:
        print("Для создания графиков установите matplotlib: pip install matplotlib") 