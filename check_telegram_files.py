#!/usr/bin/env python3
"""
Скрипт для проверки файлов в Telegram канале
Показывает что реально есть в канале
"""

import asyncio
import os
from telethon import TelegramClient
from dotenv import load_dotenv

async def check_files():
    """Проверяет файлы в канале"""
    print("🔍 ПРОВЕРКА ФАЙЛОВ В TELEGRAM КАНАЛЕ")
    print("=" * 50)
    
    # Загружаем конфигурацию
    if not os.path.exists("config.env"):
        print("❌ Файл config.env не найден!")
        return
    
    load_dotenv("config.env")
    
    api_id = int(os.getenv('TG_API_ID', 0))
    api_hash = os.getenv('TG_API_HASH', '')
    bot_token = os.getenv('TG_BOT_TOKEN', '')
    dest_chat = os.getenv('TG_DEST_CHAT', '')
    
    print(f"📱 Проверяем канал: {dest_chat}")
    print()
    
    # Подключаемся как бот
    client = TelegramClient('bot_session', api_id, api_hash)
    
    try:
        await client.start(bot_token=bot_token)
        me = await client.get_me()
        print(f"✅ Подключен как бот: {me.username}")
        print()
        
        # Получаем последние сообщения из канала
        print("📋 ФАЙЛЫ В КАНАЛЕ:")
        print("-" * 30)
        
        file_count = 0
        total_size = 0
        
        async for message in client.iter_messages(dest_chat, limit=100):
            if message.file:
                file_count += 1
                size_mb = message.file.size / (1024*1024) if message.file.size else 0
                total_size += message.file.size if message.file.size else 0
                
                print(f"📁 {message.caption or 'Без названия'}")
                print(f"   📊 Размер: {size_mb:.2f} МБ")
                print(f"   📅 Дата: {message.date}")
                print()
        
        print("=" * 50)
        print(f"📊 ИТОГО: {file_count} файлов")
        print(f"💾 Общий размер: {total_size / (1024*1024*1024):.2f} ГБ")
        
        if file_count == 0:
            print("❌ ФАЙЛОВ НЕ НАЙДЕНО!")
            print("🤔 Возможные причины:")
            print("   • Файлы были только в локальном кэше")
            print("   • Загрузка не завершилась")
            print("   • Неправильный канал")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(check_files()) 