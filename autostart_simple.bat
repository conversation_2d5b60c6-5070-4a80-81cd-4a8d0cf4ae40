@echo off
title Telegram Cloud - Настройка автозапуска и монтирования
chcp 65001 >nul
color 0E

echo.
echo 🚀 НАСТРОЙКА АВТОЗАПУСКА TELEGRAM CLOUD 🚀
echo ==========================================
echo    WebDAV сервер + Автомонтирование диска
echo.

set "PROJECT_DIR=%~dp0"
set "STARTUP_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup"
set "BATCH_FILE=%STARTUP_DIR%\TelegramCloudAutostart.bat"

echo 📁 Папка проекта: %PROJECT_DIR%
echo 📁 Папка автозагрузки: %STARTUP_DIR%
echo.

REM Создаем расширенный batch-файл для автозапуска
echo 📝 Создание файла автозапуска с монтированием диска...
(
echo @echo off
echo chcp 65001 ^>nul
echo title Telegram Cloud - Автозапуск
echo color 0A
echo.
echo echo ⚡ TELEGRAM CLOUD - АВТОЗАПУСК ⚡
echo echo ================================
echo echo Время запуска: %%date%% %%time%%
echo echo.
echo.
echo REM Переход в рабочую директорию
echo cd /d "%PROJECT_DIR%"
echo.
echo REM Проверка виртуального окружения
echo if not exist "venv\Scripts\activate.bat" ^(
echo     echo ❌ Виртуальное окружение не найдено!
echo     timeout /t 10
echo     exit /b 1
echo ^)
echo.
echo REM Активация виртуального окружения
echo echo 🔧 Активация виртуального окружения...
echo call "venv\Scripts\activate.bat"
echo.
echo REM Поиск свободной буквы диска
echo echo 🔍 Поиск свободной буквы диска...
echo set "DRIVE_LETTER="
echo for %%%%d in ^(T G H X Y Z^) do ^(
echo     if not exist "%%%%d:\" ^(
echo         set "DRIVE_LETTER=%%%%d"
echo         goto :found_drive
echo     ^)
echo ^)
echo :found_drive
echo.
echo if "%%DRIVE_LETTER%%"=="" ^(
echo     echo ⚠️ Все предпочтительные диски заняты, используется только WebDAV
echo     set "MOUNT_MODE=webdav_only"
echo ^) else ^(
echo     echo ✅ Будет использован диск %%DRIVE_LETTER%%:
echo     set "MOUNT_MODE=full"
echo ^)
echo.
echo REM Запуск WebDAV сервера
echo echo 🌐 Запуск WebDAV сервера...
echo start "Telegram WebDAV" /min python telegram_webdav.py
echo.
echo REM Ожидание запуска WebDAV
echo echo ⏳ Ожидание запуска WebDAV сервера...
echo timeout /t 5 /nobreak ^>nul
echo.
echo REM Проверка и монтирование диска ^(если возможно^)
echo if "%%MOUNT_MODE%%"=="full" ^(
echo     rclone version ^>nul 2^>^&1
echo     if not errorlevel 1 ^(
echo         echo 💾 Монтирование диска %%DRIVE_LETTER%%:...
echo         start "Telegram Mount" /min rclone mount telegram: %%DRIVE_LETTER%%: --vfs-cache-mode writes --vfs-cache-max-size 1G --dir-cache-time 5m -v
echo         
echo         REM Ожидание монтирования
echo         for /l %%%%i in ^(1,1,10^) do ^(
echo             timeout /t 1 /nobreak ^>nul
echo             if exist "%%DRIVE_LETTER%%:\" ^(
echo                 echo ✅ Диск %%DRIVE_LETTER%%: готов!
echo                 goto :mount_ready
echo             ^)
echo         ^)
echo         echo ⚠️ Диск монтируется в фоне...
echo         :mount_ready
echo     ^) else ^(
echo         echo ⚠️ rclone не найден - только WebDAV режим
echo     ^)
echo ^)
echo.
echo echo ✅ Telegram Cloud запущен!
echo if "%%MOUNT_MODE%%"=="full" ^(
echo     echo 📁 Диск: %%DRIVE_LETTER%%:\
echo ^)
echo echo 🌐 WebDAV: http://localhost:8080
echo echo.
echo echo 🔄 Работает в фоне... ^(свернуто в трей^)
echo echo ⚠️ Для остановки найдите в диспетчере задач
echo.
echo REM Минимизируем окно через 10 секунд
echo timeout /t 10 /nobreak ^>nul
echo.
echo REM Переход в фоновый режим
echo :background_loop
echo timeout /t 300 /nobreak ^>nul
echo goto :background_loop
) > "%BATCH_FILE%"

if exist "%BATCH_FILE%" (
    echo ✅ Автозапуск настроен успешно!
    echo    Файл: %BATCH_FILE%
    echo.
    echo 💡 WebDAV-сервер будет запускаться при входе в Windows
    echo    Для отключения удалите файл из папки автозагрузки
) else (
    echo ❌ Ошибка создания файла автозапуска
)

echo.
echo Нажмите любую клавишу для выхода...
pause > nul 