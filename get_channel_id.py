#!/usr/bin/env python3
"""
get_channel_id.py - Получение ID каналов для Telegram бота
========================================================

Этот скрипт покажет все каналы и чаты, доступные боту,
чтобы вы могли выбрать подходящий для хранения файлов.
"""

import asyncio
import os
from telethon import TelegramClient
from dotenv import load_dotenv

def load_config():
    """Загрузка конфигурации"""
    load_dotenv('config.env')
    
    return {
        'api_id': int(os.getenv('TG_API_ID', 0)),
        'api_hash': os.getenv('TG_API_HASH', ''),
        'bot_token': os.getenv('TG_BOT_TOKEN', ''),
    }

async def get_channels_info():
    """Получить информацию о всех доступных каналах"""
    config = load_config()
    
    if not config['api_id'] or not config['api_hash']:
        print("❌ Ошибка: TG_API_ID и TG_API_HASH не настроены в config.env")
        return
    
    if not config['bot_token']:
        print("❌ Ошибка: TG_BOT_TOKEN не настроен в config.env")
        return
    
    print("🤖 Подключение к Telegram как бот...")
    print("=" * 60)
    
    client = TelegramClient('bot_session', config['api_id'], config['api_hash'])
    
    try:
        await client.start(bot_token=config['bot_token'])
        me = await client.get_me()
        print(f"✅ Подключен как: {me.username} ({me.first_name})")
        print()
        
        print("📋 ДОСТУПНЫЕ КАНАЛЫ И ЧАТЫ:")
        print("=" * 60)
        
        channels_found = False
        groups_found = False
        
        async for dialog in client.iter_dialogs():
            entity = dialog.entity
            
            # Каналы
            if hasattr(entity, 'broadcast') and entity.broadcast:
                if not channels_found:
                    print("📢 КАНАЛЫ:")
                    channels_found = True
                
                username = f"@{entity.username}" if entity.username else "нет username"
                print(f"   📺 {dialog.name}")
                print(f"      ID: {dialog.id}")
                print(f"      Username: {username}")
                print(f"      Участников: {getattr(entity, 'participants_count', 'неизвестно')}")
                print()
            
            # Группы
            elif hasattr(entity, 'participants_count'):
                if not groups_found:
                    print("👥 ГРУППЫ:")
                    groups_found = True
                
                username = f"@{entity.username}" if entity.username else "нет username"
                print(f"   💬 {dialog.name}")
                print(f"      ID: {dialog.id}")
                print(f"      Username: {username}")
                print(f"      Участников: {entity.participants_count}")
                print()
        
        if not channels_found and not groups_found:
            print("⚠️  Бот не добавлен ни в какие каналы или группы")
            print()
            print("📝 ЧТО ДЕЛАТЬ:")
            print("1. Создайте новый канал в Telegram")
            print("2. Добавьте бота @{} в канал как администратора".format(me.username))
            print("3. Дайте боту права: 'Отправка сообщений', 'Удаление сообщений'")
            print("4. Запустите этот скрипт снова")
        else:
            print("🎯 КАК ИСПОЛЬЗОВАТЬ:")
            print("1. Выберите подходящий канал или группу из списка выше")
            print("2. Скопируйте его ID (например: -1001234567890)")
            print("3. Вставьте в config.env: TG_DEST_CHAT=-1001234567890")
            print("   Или используйте username: TG_DEST_CHAT=@channel_name")
        
        print()
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Ошибка подключения: {e}")
        
    finally:
        await client.disconnect()

def main():
    """Главная функция"""
    print("🔍 ПОИСК КАНАЛОВ ДЛЯ TELEGRAM БОТА")
    print("=" * 60)
    
    # Проверка файла конфигурации
    if not os.path.exists('config.env'):
        print("❌ Файл config.env не найден!")
        print("   Создайте config.env из config_example.env")
        return
    
    try:
        asyncio.run(get_channels_info())
    except KeyboardInterrupt:
        print("\n⚠️  Прервано пользователем")
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")

if __name__ == "__main__":
    main() 