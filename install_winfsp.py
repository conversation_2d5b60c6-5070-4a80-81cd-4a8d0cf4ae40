#!/usr/bin/env python3
"""
install_winfsp.py - Автоматическая установка WinFsp для Windows
WinFsp нужен для монтирования rclone как диска
"""

import os
import sys
import subprocess
import tempfile
import requests
from pathlib import Path

def download_file(url, filename):
    """Скачать файл с прогресс-баром."""
    print(f"Скачиваю {filename}...")
    
    response = requests.get(url, stream=True)
    response.raise_for_status()
    
    total_size = int(response.headers.get('content-length', 0))
    downloaded = 0
    
    with open(filename, 'wb') as f:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                f.write(chunk)
                downloaded += len(chunk)
                if total_size > 0:
                    percent = (downloaded / total_size) * 100
                    print(f"\rПрогресс: {percent:.1f}%", end='', flush=True)
    
    print(f"\n✅ {filename} скачан")

def check_winfsp():
    """Проверить, установлен ли WinFsp."""
    # Проверяем через реестр
    try:
        import winreg
        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                           r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall")
        
        i = 0
        while True:
            try:
                subkey_name = winreg.EnumKey(key, i)
                subkey = winreg.OpenKey(key, subkey_name)
                try:
                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                    if "WinFsp" in display_name:
                        print(f"✅ WinFsp уже установлен: {display_name}")
                        winreg.CloseKey(subkey)
                        winreg.CloseKey(key)
                        return True
                except:
                    pass
                winreg.CloseKey(subkey)
                i += 1
            except WindowsError:
                break
        winreg.CloseKey(key)
    except:
        pass
    
    # Проверяем наличие файлов
    winfsp_paths = [
        r"C:\Program Files (x86)\WinFsp\bin\winfsp-x64.dll",
        r"C:\Program Files\WinFsp\bin\winfsp-x64.dll"
    ]
    
    for path in winfsp_paths:
        if Path(path).exists():
            print(f"✅ WinFsp найден: {path}")
            return True
    
    return False

def install_winfsp():
    """Установить WinFsp."""
    print("🔧 Установка WinFsp...")
    
    # URL последней версии WinFsp
    winfsp_url = "https://github.com/billziss-gh/winfsp/releases/download/v2.1/winfsp-2.1.24047.msi"
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        installer_file = temp_path / "winfsp.msi"
        
        try:
            # Скачиваем установщик
            download_file(winfsp_url, installer_file)
            
            # Запускаем установку
            print("📦 Запуск установки WinFsp...")
            print("⚠️  ВНИМАНИЕ: Может потребоваться подтверждение UAC")
            
            install_cmd = [
                "msiexec", "/i", str(installer_file),
                "/quiet", "/norestart",
                "INSTALLLEVEL=1000"  # Полная установка
            ]
            
            print("Установка может занять несколько минут...")
            result = subprocess.run(install_cmd, timeout=300)
            
            if result.returncode == 0:
                print("✅ WinFsp установлен успешно!")
                print("🔄 Может потребоваться перезагрузка системы")
                return True
            elif result.returncode == 3010:
                print("✅ WinFsp установлен, требуется перезагрузка")
                return True
            else:
                print(f"❌ Ошибка установки, код: {result.returncode}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Установка прервана по таймауту")
            return False
        except Exception as e:
            print(f"❌ Ошибка установки: {e}")
            return False

def main():
    print("🚀 Установка WinFsp для монтирования дисков")
    print("=" * 50)
    
    if not os.name == 'nt':
        print("❌ Этот скрипт работает только в Windows")
        return False
    
    # Проверяем, нужна ли установка
    if check_winfsp():
        choice = input("WinFsp уже установлен. Переустановить? (y/N): ").strip().lower()
        if choice not in ['y', 'yes', 'д', 'да']:
            return True
    
    # Проверяем зависимости
    try:
        import requests
        import winreg
    except ImportError as e:
        missing = str(e).split("'")[1]
        print(f"📦 Устанавливаю зависимость: {missing}")
        subprocess.check_call([sys.executable, "-m", "pip", "install", missing])
        if missing == "requests":
            import requests
    
    # Устанавливаем WinFsp
    success = install_winfsp()
    
    if success:
        print("\n🎉 WinFsp установлен!")
        print("📋 Следующие шаги:")
        print("1. Возможно, потребуется перезагрузка")
        print("2. python mount_telegram.py  # Создание скрипта монтирования")
        print("3. Монтирование Telegram как диска")
        return True
    else:
        print("\n❌ Установка WinFsp не удалась")
        print("Попробуйте установить вручную с https://winfsp.dev/rel/")
        return False

if __name__ == "__main__":
    main() 