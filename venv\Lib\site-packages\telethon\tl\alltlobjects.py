"""File generated by <PERSON><PERSON><PERSON><PERSON><PERSON>' generator. All changes will be ERASED"""

from . import types, functions

LAYER = 201

tlobjects = {
    0xf3ed4c73: functions.account.AcceptAuthorizationRequest,
    0x3bd2b4a0: functions.phone.AcceptCallRequest,
    0xf831a20f: functions.contacts.AcceptContactRequest,
    0x3dbc0415: functions.messages.AcceptEncryptionRequest,
    0xe894ad4d: functions.auth.AcceptLoginTokenRequest,
    0xee72f79a: functions.help.AcceptTermsOfServiceRequest,
    0xb12c7125: functions.messages.AcceptUrlAuthRequest,
    0x4679b65f: types.AccessPointRule,
    0xb8d0afdf: types.AccountDaysTTL,
    0x57bbd166: functions.stories.ActivateStealthModeRequest,
    0xcbc6d107: functions.messages.AddChatUserRequest,
    0xe8f463d0: functions.contacts.AddContactRequest,
    0x6f688aa7: functions.account.AddNoPaidMessagesExceptionRequest,
    0x17aeb75a: functions.bots.AddPreviewMediaRequest,
    0x8653febe: functions.stickers.AddStickerToSetRequest,
    0xed8af74d: types.channels.AdminLogResults,
    0xef8d3e6c: types.messages.AffectedFoundMessages,
    0xb45c69d1: types.messages.AffectedHistory,
    0x84d19185: types.messages.AffectedMessages,
    0xcdbbcebb: types.messages.AllStickers,
    0xe86602c3: types.messages.AllStickersNotModified,
    0x6efc5e81: types.stories.AllStories,
    0x1158fe3e: types.stories.AllStoriesNotModified,
    0xf132e3ef: functions.bots.AllowSendMessageRequest,
    0xe6213f4d: functions.bots.AnswerWebhookJSONQueryRequest,
    0xdd18782e: types.help.AppConfig,
    0x7cde641d: types.help.AppConfigNotModified,
    0xccbbce30: types.help.AppUpdate,
    0x6b7da746: functions.premium.ApplyBoostRequest,
    0xf6e26854: functions.payments.ApplyGiftCodeRequest,
    0x4fcba9c8: types.messages.ArchivedStickers,
    0x80ed747d: functions.payments.AssignAppStoreTransactionRequest,
    0xdffd50d3: functions.payments.AssignPlayMarketTransactionRequest,
    0xd90d8dfe: types.AttachMenuBot,
    0xb2a7386b: types.AttachMenuBotIcon,
    0x4576f3f0: types.AttachMenuBotIconColor,
    0x3c4301c0: types.AttachMenuBots,
    0x93bf667f: types.AttachMenuBotsBot,
    0xf1d88a5c: types.AttachMenuBotsNotModified,
    0xc32bfa1a: types.AttachMenuPeerTypeBotPM,
    0x7bfbdefc: types.AttachMenuPeerTypeBroadcast,
    0x0509113f: types.AttachMenuPeerTypeChat,
    0xf146d31f: types.AttachMenuPeerTypePM,
    0x7d6be90e: types.AttachMenuPeerTypeSameBotPM,
    0x2ea2c0d4: types.auth.Authorization,
    0xad01d61d: types.Authorization,
    0xad2e1cd8: types.account.AuthorizationForm,
    0x44747e9a: types.auth.AuthorizationSignUpRequired,
    0x4bff8ea0: types.account.Authorizations,
    0xbaa57628: types.AutoDownloadSettings,
    0x63cacf26: types.account.AutoDownloadSettings,
    0x81602d47: types.AutoSaveException,
    0xc84834ce: types.AutoSaveSettings,
    0x4c3e069d: types.account.AutoSaveSettings,
    0x93c3e27e: types.AvailableEffect,
    0xbddb616e: types.messages.AvailableEffects,
    0xd1ed9a5b: types.messages.AvailableEffectsNotModified,
    0xc077ec01: types.AvailableReaction,
    0x768e3aad: types.messages.AvailableReactions,
    0x9f071957: types.messages.AvailableReactionsNotModified,
    0xa7eff811: types.BadMsgNotification,
    0xedab447b: types.BadServerSalt,
    0x3e24e573: types.payments.BankCardData,
    0xf568028a: types.BankCardOpenUrl,
    0x5b11125a: types.BaseThemeArctic,
    0xc3a12462: types.BaseThemeClassic,
    0xfbd81688: types.BaseThemeDay,
    0xb7b31ea8: types.BaseThemeNight,
    0x6d5f77ee: types.BaseThemeTinted,
    0xcdd42a05: functions.auth.BindTempAuthKeyRequest,
    0x75a3f765: types.BindAuthKeyInner,
    0x6c8e1e06: types.Birthday,
    0x2e2e8734: functions.contacts.BlockRequest,
    0x29a8962c: functions.contacts.BlockFromRepliesRequest,
    0x0ade1591: types.contacts.Blocked,
    0xe1664194: types.contacts.BlockedSlice,
    0x4b3e14d6: types.Boost,
    0x86f8613c: types.premium.BoostsList,
    0x4959427a: types.premium.BoostsStatus,
    0x95fcd1d6: types.BotApp,
    0xeb50adf5: types.messages.BotApp,
    0x5da674b7: types.BotAppNotModified,
    0xc99b1950: types.BotAppSettings,
    0x8f34b2f5: types.BotBusinessConnection,
    0x36585ea4: types.messages.BotCallbackAnswer,
    0x6dfa0622: functions.payments.BotCancelStarsSubscriptionRequest,
    0xc27ac8c7: types.BotCommand,
    0xb9aa606a: types.BotCommandScopeChatAdmins,
    0x6fe1a881: types.BotCommandScopeChats,
    0x2f6cb2ab: types.BotCommandScopeDefault,
    0xdb9d897d: types.BotCommandScopePeer,
    0x3fd863d1: types.BotCommandScopePeerAdmins,
    0x0a1321f3: types.BotCommandScopePeerUser,
    0x3c4f04d8: types.BotCommandScopeUsers,
    0x4d8a0299: types.BotInfo,
    0xe8a775b0: types.bots.BotInfo,
    0x17db940b: types.BotInlineMediaResult,
    0x764cf810: types.BotInlineMessageMediaAuto,
    0x18d1cdc2: types.BotInlineMessageMediaContact,
    0x051846fd: types.BotInlineMessageMediaGeo,
    0x354a9b09: types.BotInlineMessageMediaInvoice,
    0x8a86659c: types.BotInlineMessageMediaVenue,
    0x809ad9a6: types.BotInlineMessageMediaWebPage,
    0x8c7f65e2: types.BotInlineMessageText,
    0x11965f3a: types.BotInlineResult,
    0xc7b57ce6: types.BotMenuButton,
    0x4258c205: types.BotMenuButtonCommands,
    0x7533a588: types.BotMenuButtonDefault,
    0x8ecf0511: types.messages.BotPreparedInlineMessage,
    0x23e91ba3: types.BotPreviewMedia,
    0xe021f2f6: types.messages.BotResults,
    0xf93cd45c: types.BotVerification,
    0xb0cd6617: types.BotVerifierSettings,
    0xc3ff71e7: types.BroadcastRevenueBalances,
    0x5407e297: types.stats.BroadcastRevenueStats,
    0x557e2cc4: types.BroadcastRevenueTransactionProceeds,
    0x42d30d2e: types.BroadcastRevenueTransactionRefund,
    0x5a590978: types.BroadcastRevenueTransactionWithdrawal,
    0x87158466: types.stats.BroadcastRevenueTransactions,
    0xec659737: types.stats.BroadcastRevenueWithdrawalUrl,
    0x396ca5fc: types.stats.BroadcastStats,
    0xef156a5c: types.BusinessAwayMessage,
    0xc9b9e2b9: types.BusinessAwayMessageScheduleAlways,
    0xcc4d9ecc: types.BusinessAwayMessageScheduleCustom,
    0xc3f2f501: types.BusinessAwayMessageScheduleOutsideWorkHours,
    0xb88cf373: types.BusinessBotRecipients,
    0xa0624cf7: types.BusinessBotRights,
    0xb4ae666f: types.BusinessChatLink,
    0xec43a2d1: types.account.BusinessChatLinks,
    0xe519abab: types.BusinessGreetingMessage,
    0x5a0a066d: types.BusinessIntro,
    0xac5c1af7: types.BusinessLocation,
    0x21108ff7: types.BusinessRecipients,
    0x120b1ab9: types.BusinessWeeklyOpen,
    0x8c92b098: types.BusinessWorkHours,
    0x4fdc5ea7: functions.payments.CanPurchaseStoreRequest,
    0x1359f4e6: functions.bots.CanSendMessageRequest,
    0xc7dfdfdd: functions.stories.CanSendStoryRequest,
    0x1f040578: functions.auth.CancelCodeRequest,
    0xc1cbd5b6: functions.account.CancelPasswordEmailRequest,
    0x5725e40a: types.CdnConfig,
    0xa99fca4f: types.upload.CdnFile,
    0xeea8e46e: types.upload.CdnFileReuploadNeeded,
    0xc982eaba: types.CdnPublicKey,
    0x40f48462: functions.account.ChangeAuthorizationSettingsRequest,
    0x70c32edb: functions.account.ChangePhoneRequest,
    0xc7770878: functions.payments.ChangeStarsSubscriptionRequest,
    0xf5537ebc: functions.stickers.ChangeStickerRequest,
    0xffb6d4ca: functions.stickers.ChangeStickerPositionRequest,
    0x7482147e: types.Channel,
    0x1fad68cd: types.ChannelAdminLogEvent,
    0x55188a2e: types.ChannelAdminLogEventActionChangeAbout,
    0xbe4e0ef8: types.ChannelAdminLogEventActionChangeAvailableReactions,
    0x3ea9feb1: types.ChannelAdminLogEventActionChangeEmojiStatus,
    0x46d840ab: types.ChannelAdminLogEventActionChangeEmojiStickerSet,
    0x6e941a38: types.ChannelAdminLogEventActionChangeHistoryTTL,
    0x050c7ac8: types.ChannelAdminLogEventActionChangeLinkedChat,
    0x0e6b76ae: types.ChannelAdminLogEventActionChangeLocation,
    0x5796e780: types.ChannelAdminLogEventActionChangePeerColor,
    0x434bd2af: types.ChannelAdminLogEventActionChangePhoto,
    0x5e477b25: types.ChannelAdminLogEventActionChangeProfilePeerColor,
    0xb1c3caa7: types.ChannelAdminLogEventActionChangeStickerSet,
    0xe6dfb825: types.ChannelAdminLogEventActionChangeTitle,
    0x6a4afc38: types.ChannelAdminLogEventActionChangeUsername,
    0xf04fb3a9: types.ChannelAdminLogEventActionChangeUsernames,
    0x31bb5d52: types.ChannelAdminLogEventActionChangeWallpaper,
    0x58707d28: types.ChannelAdminLogEventActionCreateTopic,
    0x2df5fc0a: types.ChannelAdminLogEventActionDefaultBannedRights,
    0x42e047bb: types.ChannelAdminLogEventActionDeleteMessage,
    0xae168909: types.ChannelAdminLogEventActionDeleteTopic,
    0xdb9f9140: types.ChannelAdminLogEventActionDiscardGroupCall,
    0x709b2405: types.ChannelAdminLogEventActionEditMessage,
    0xf06fe208: types.ChannelAdminLogEventActionEditTopic,
    0x5a50fca4: types.ChannelAdminLogEventActionExportedInviteDelete,
    0xe90ebb59: types.ChannelAdminLogEventActionExportedInviteEdit,
    0x410a134e: types.ChannelAdminLogEventActionExportedInviteRevoke,
    0xe31c34d8: types.ChannelAdminLogEventActionParticipantInvite,
    0x183040d3: types.ChannelAdminLogEventActionParticipantJoin,
    0xfe9fc158: types.ChannelAdminLogEventActionParticipantJoinByInvite,
    0xafb6144a: types.ChannelAdminLogEventActionParticipantJoinByRequest,
    0xf89777f2: types.ChannelAdminLogEventActionParticipantLeave,
    0xf92424d2: types.ChannelAdminLogEventActionParticipantMute,
    0x64642db3: types.ChannelAdminLogEventActionParticipantSubExtend,
    0xd5676710: types.ChannelAdminLogEventActionParticipantToggleAdmin,
    0xe6d83d7e: types.ChannelAdminLogEventActionParticipantToggleBan,
    0xe64429c0: types.ChannelAdminLogEventActionParticipantUnmute,
    0x3e7f6847: types.ChannelAdminLogEventActionParticipantVolume,
    0x5d8d353b: types.ChannelAdminLogEventActionPinTopic,
    0x278f2868: types.ChannelAdminLogEventActionSendMessage,
    0x23209745: types.ChannelAdminLogEventActionStartGroupCall,
    0x8f079643: types.ChannelAdminLogEventActionStopPoll,
    0x64f36dfc: types.ChannelAdminLogEventActionToggleAntiSpam,
    0x02cc6383: types.ChannelAdminLogEventActionToggleForum,
    0x56d6a247: types.ChannelAdminLogEventActionToggleGroupCallSetting,
    0x1b7907ae: types.ChannelAdminLogEventActionToggleInvites,
    0xcb2ac766: types.ChannelAdminLogEventActionToggleNoForwards,
    0x5f5c95f1: types.ChannelAdminLogEventActionTogglePreHistoryHidden,
    0x60a79c79: types.ChannelAdminLogEventActionToggleSignatureProfiles,
    0x26ae0971: types.ChannelAdminLogEventActionToggleSignatures,
    0x53909779: types.ChannelAdminLogEventActionToggleSlowMode,
    0xe9e82c18: types.ChannelAdminLogEventActionUpdatePinned,
    0xea107ae4: types.ChannelAdminLogEventsFilter,
    0x2064674e: types.updates.ChannelDifference,
    0x3e11affb: types.updates.ChannelDifferenceEmpty,
    0xa4bcc6fe: types.updates.ChannelDifferenceTooLong,
    0x17d493d5: types.ChannelForbidden,
    0x52d6806b: types.ChannelFull,
    0x209b82db: types.ChannelLocation,
    0xbfb5ad8b: types.ChannelLocationEmpty,
    0xc776ba4e: types.messages.ChannelMessages,
    0xcd77d957: types.ChannelMessagesFilter,
    0x94d42ee7: types.ChannelMessagesFilterEmpty,
    0xcb397619: types.ChannelParticipant,
    0xdfb80317: types.channels.ChannelParticipant,
    0x34c3bb53: types.ChannelParticipantAdmin,
    0x6df8014e: types.ChannelParticipantBanned,
    0x2fe601d3: types.ChannelParticipantCreator,
    0x1b03f006: types.ChannelParticipantLeft,
    0x4f607bef: types.ChannelParticipantSelf,
    0x9ab0feaf: types.channels.ChannelParticipants,
    0xb4608969: types.ChannelParticipantsAdmins,
    0x1427a5e1: types.ChannelParticipantsBanned,
    0xb0d1865b: types.ChannelParticipantsBots,
    0xbb6ae88d: types.ChannelParticipantsContacts,
    0xa3b54985: types.ChannelParticipantsKicked,
    0xe04b5ceb: types.ChannelParticipantsMentions,
    0xf0173fe9: types.channels.ChannelParticipantsNotModified,
    0xde3f3c79: types.ChannelParticipantsRecent,
    0x0656ac4b: types.ChannelParticipantsSearch,
    0x41cbf256: types.Chat,
    0x5fb224d5: types.ChatAdminRights,
    0xf2ecef23: types.ChatAdminWithInvites,
    0xb69b72d7: types.messages.ChatAdminsWithInvites,
    0x9f120418: types.ChatBannedRights,
    0x29562865: types.ChatEmpty,
    0x6592a1a7: types.ChatForbidden,
    0x2633421b: types.ChatFull,
    0xe5d7d19c: types.messages.ChatFull,
    0x5c9d3702: types.ChatInvite,
    0x5a686d7c: types.ChatInviteAlready,
    0xa22cbd96: types.ChatInviteExported,
    0x8c5adfd9: types.ChatInviteImporter,
    0x81b6b00a: types.messages.ChatInviteImporters,
    0x61695cb0: types.ChatInvitePeek,
    0xed107ab7: types.ChatInvitePublicJoinRequests,
    0xf041e250: types.ChatOnlines,
    0xc02d4007: types.ChatParticipant,
    0xa0933f5b: types.ChatParticipantAdmin,
    0xe46bcee4: types.ChatParticipantCreator,
    0x3cbc93f8: types.ChatParticipants,
    0x8763d3e1: types.ChatParticipantsForbidden,
    0x1c6e1c11: types.ChatPhoto,
    0x37c1011c: types.ChatPhotoEmpty,
    0x52928bca: types.ChatReactionsAll,
    0xeafc32bc: types.ChatReactionsNone,
    0x661d4037: types.ChatReactionsSome,
    0xf10ece2f: types.chatlists.ChatlistInvite,
    0xfa87f659: types.chatlists.ChatlistInviteAlready,
    0x93bd878d: types.chatlists.ChatlistUpdates,
    0x64ff9fd5: types.messages.Chats,
    0x9cd81144: types.messages.ChatsSlice,
    0x3eadb1bb: functions.messages.CheckChatInviteRequest,
    0x41c10fff: functions.chatlists.CheckChatlistInviteRequest,
    0x50077589: functions.bots.CheckDownloadFileParamsRequest,
    0x8e51b4c1: functions.payments.CheckGiftCodeRequest,
    0xb59cf977: functions.phone.CheckGroupCallRequest,
    0x43fe19f3: functions.messages.CheckHistoryImportRequest,
    0x5dc60f03: functions.messages.CheckHistoryImportPeerRequest,
    0xd18b4d16: functions.auth.CheckPasswordRequest,
    0xf1d0fbd3: functions.messages.CheckQuickReplyShortcutRequest,
    0x0d36bf79: functions.auth.CheckRecoveryPasswordRequest,
    0x284b3639: functions.stickers.CheckShortNameRequest,
    0x2714d86c: functions.account.CheckUsernameRequest,
    0x10e6bd2c: functions.channels.CheckUsernameRequest,
    0x284a1096: types.payments.CheckedGiftCode,
    0xa24de717: types.messages.CheckedHistoryImportPeer,
    0x7e58ee9c: functions.messages.ClearAllDraftsRequest,
    0x18201aae: functions.account.ClearRecentEmojiStatusesRequest,
    0x9dfeefb4: functions.messages.ClearRecentReactionsRequest,
    0x8999602d: functions.messages.ClearRecentStickersRequest,
    0xd83d70c1: functions.payments.ClearSavedInfoRequest,
    0x8235057e: functions.messages.ClickSponsoredMessageRequest,
    0x6643b654: types.ClientDHInnerData,
    0xad253d78: types.CodeSettings,
    0x741cd3e3: types.auth.CodeTypeCall,
    0x226ccefb: types.auth.CodeTypeFlashCall,
    0x06ed998c: types.auth.CodeTypeFragmentSms,
    0xd61ad6ee: types.auth.CodeTypeMissedCall,
    0x72a3158c: types.auth.CodeTypeSms,
    0x6ebdff91: types.fragment.CollectibleInfo,
    0xcc1a241e: types.Config,
    0x5a592a6c: types.help.ConfigSimple,
    0x2efe1722: functions.phone.ConfirmCallRequest,
    0x8fdf1920: functions.account.ConfirmPasswordEmailRequest,
    0x5f2178c3: functions.account.ConfirmPhoneRequest,
    0x7ed5348a: functions.payments.ConnectStarRefBotRequest,
    0xcd64636c: types.ConnectedBot,
    0x19a13f71: types.ConnectedBotStarRef,
    0x17d7f87b: types.account.ConnectedBots,
    0x98d5ea1d: types.payments.ConnectedStarRefBots,
    0x145ade0b: types.Contact,
    0x1d998733: types.ContactBirthday,
    0x114ff30d: types.contacts.ContactBirthdays,
    0x16d9703b: types.ContactStatus,
    0xeae87e42: types.contacts.Contacts,
    0xb74ba9d2: types.contacts.ContactsNotModified,
    0x57e28221: types.account.ContentSettings,
    0x74bf076b: functions.payments.ConvertStarGiftRequest,
    0x0b290c69: functions.channels.ConvertToGigagroupRequest,
    0x87d0759e: types.help.CountriesList,
    0x93cc1f32: types.help.CountriesListNotModified,
    0xc3878e23: types.help.Country,
    0x4203c5ef: types.help.CountryCode,
    0x8851e68e: functions.account.CreateBusinessChatLinkRequest,
    0x91006707: functions.channels.CreateChannelRequest,
    0x92ceddd4: functions.messages.CreateChatRequest,
    0xdfc909ab: functions.phone.CreateConferenceCallRequest,
    0xf40c0224: functions.channels.CreateForumTopicRequest,
    0x48cdc6d8: functions.phone.CreateGroupCallRequest,
    0x9021ab67: functions.stickers.CreateStickerSetRequest,
    0x652e4400: functions.account.CreateThemeRequest,
    0x7d748d04: types.DataJSON,
    0x18b7a10d: types.DcOption,
    0x0a245dd3: functions.channels.DeactivateAllUsernamesRequest,
    0x4c9409f6: functions.account.DeclinePasswordResetRequest,
    0x6a4ee832: types.help.DeepLinkInfo,
    0x66afa166: types.help.DeepLinkInfoEmpty,
    0x43b46b20: types.DefaultHistoryTTL,
    0xa2c0cf74: functions.account.DeleteAccountRequest,
    0x53bc0020: functions.account.DeleteAutoSaveExceptionsRequest,
    0x60073674: functions.account.DeleteBusinessChatLinkRequest,
    0x1013fd9e: functions.contacts.DeleteByPhonesRequest,
    0xc0111fe3: functions.channels.DeleteChannelRequest,
    0x5bd0ee50: functions.messages.DeleteChatRequest,
    0xa2185cab: functions.messages.DeleteChatUserRequest,
    0x096a0e00: functions.contacts.DeleteContactsRequest,
    0xd464a42b: functions.messages.DeleteExportedChatInviteRequest,
    0x719c5c5e: functions.chatlists.DeleteExportedInviteRequest,
    0xd1da940c: functions.messages.DeleteFactCheckRequest,
    0xb08f922a: functions.messages.DeleteHistoryRequest,
    0x9baa9647: functions.channels.DeleteHistoryRequest,
    0xe58e95d2: functions.messages.DeleteMessagesRequest,
    0x84c1fd4e: functions.channels.DeleteMessagesRequest,
    0x367544db: functions.channels.DeleteParticipantHistoryRequest,
    0xf9cbe409: functions.messages.DeletePhoneCallHistoryRequest,
    0x87cf7f2f: functions.photos.DeletePhotosRequest,
    0x2d0135b3: functions.bots.DeletePreviewMediaRequest,
    0xe105e910: functions.messages.DeleteQuickReplyMessagesRequest,
    0x3cc04740: functions.messages.DeleteQuickReplyShortcutRequest,
    0x56987bd5: functions.messages.DeleteRevokedExportedChatInvitesRequest,
    0x6e98102b: functions.messages.DeleteSavedHistoryRequest,
    0x59ae2b16: functions.messages.DeleteScheduledMessagesRequest,
    0xb880bc4b: functions.account.DeleteSecureValueRequest,
    0x87704394: functions.stickers.DeleteStickerSetRequest,
    0xae59db5f: functions.stories.DeleteStoriesRequest,
    0x34435f2d: functions.channels.DeleteTopicHistoryRequest,
    0xd1435160: functions.DestroyAuthKeyRequest,
    0xea109b13: types.DestroyAuthKeyFail,
    0x0a9f2259: types.DestroyAuthKeyNone,
    0xf660e1d4: types.DestroyAuthKeyOk,
    0xe7512126: functions.DestroySessionRequest,
    0x62d350c9: types.DestroySessionNone,
    0xe22045fc: types.DestroySessionOk,
    0x2c221edd: types.messages.DhConfig,
    0xc0e24635: types.messages.DhConfigNotModified,
    0xa69dae02: types.DhGenFail,
    0x3bcbf734: types.DhGenOk,
    0x46dc1fb9: types.DhGenRetry,
    0xd58a08c6: types.Dialog,
    0xaa472651: types.DialogFilter,
    0x96537bd7: types.DialogFilterChatlist,
    0x363293ae: types.DialogFilterDefault,
    0x77744d4a: types.DialogFilterSuggested,
    0x2ad93719: types.messages.DialogFilters,
    0x71bd134c: types.DialogFolder,
    0xe56dbf05: types.DialogPeer,
    0x514519e2: types.DialogPeerFolder,
    0x15ba6c40: types.messages.Dialogs,
    0xf0e3e596: types.messages.DialogsNotModified,
    0x71e094f3: types.messages.DialogsSlice,
    0x00f49ca0: types.updates.Difference,
    0x5d75a138: types.updates.DifferenceEmpty,
    0xa8fb1981: types.updates.DifferenceSlice,
    0x4afe8f6d: types.updates.DifferenceTooLong,
    0x5e437ed9: functions.account.DisablePeerConnectedBotRequest,
    0x71f276c4: types.DisallowedGiftsSettings,
    0xb2cbc1c0: functions.phone.DiscardCallRequest,
    0xf393aea0: functions.messages.DiscardEncryptionRequest,
    0x7a777135: functions.phone.DiscardGroupCallRequest,
    0xa6341782: types.messages.DiscussionMessage,
    0xf50dbaa1: functions.help.DismissSuggestionRequest,
    0x8fd4c4d8: types.Document,
    0x11b58939: types.DocumentAttributeAnimated,
    0x9852f9c6: types.DocumentAttributeAudio,
    0xfd149899: types.DocumentAttributeCustomEmoji,
    0x15590068: types.DocumentAttributeFilename,
    0x9801d2f7: types.DocumentAttributeHasStickers,
    0x6c37c15c: types.DocumentAttributeImageSize,
    0x6319d612: types.DocumentAttributeSticker,
    0x43c57c48: types.DocumentAttributeVideo,
    0x36f8c871: types.DocumentEmpty,
    0x2d65321f: types.DraftMessage,
    0x1b0c841a: types.DraftMessageEmpty,
    0x8e48a188: functions.auth.DropTempAuthKeysRequest,
    0xd33c8902: functions.channels.EditAdminRequest,
    0x96e6cd81: functions.channels.EditBannedRequest,
    0x8c3410af: functions.account.EditBusinessChatLinkRequest,
    0xdef60797: functions.messages.EditChatAboutRequest,
    0xa85bd1c2: functions.messages.EditChatAdminRequest,
    0xa5866b41: functions.messages.EditChatDefaultBannedRightsRequest,
    0x35ddd674: functions.messages.EditChatPhotoRequest,
    0x73783ffd: functions.messages.EditChatTitleRequest,
    0xba6705f0: functions.contacts.EditCloseFriendsRequest,
    0xe4fca4a3: functions.payments.EditConnectedStarRefBotRequest,
    0x8f38cd1f: functions.channels.EditCreatorRequest,
    0xbdca2f75: functions.messages.EditExportedChatInviteRequest,
    0x653db63d: functions.chatlists.EditExportedInviteRequest,
    0x0589ee75: functions.messages.EditFactCheckRequest,
    0xf4dfa185: functions.channels.EditForumTopicRequest,
    0xa5273abf: functions.phone.EditGroupCallParticipantRequest,
    0x1ca6ac0a: functions.phone.EditGroupCallTitleRequest,
    0x83557dba: functions.messages.EditInlineBotMessageRequest,
    0x58e63f6d: functions.channels.EditLocationRequest,
    0xdfd14005: functions.messages.EditMessageRequest,
    0x6847d0ab: functions.folders.EditPeerFoldersRequest,
    0xf12e57c9: functions.channels.EditPhotoRequest,
    0x8525606f: functions.bots.EditPreviewMediaRequest,
    0x5c003cef: functions.messages.EditQuickReplyShortcutRequest,
    0xb583ba46: functions.stories.EditStoryRequest,
    0x566decd0: functions.channels.EditTitleRequest,
    0x66b91b70: functions.help.EditUserInfoRequest,
    0xdc8b44cf: types.smsjobs.EligibleToJoin,
    0x96d074fd: types.EmailVerificationApple,
    0x922e55a9: types.EmailVerificationCode,
    0xdb909ec2: types.EmailVerificationGoogle,
    0x2b96cd1b: types.account.EmailVerified,
    0xe1bb0d61: types.account.EmailVerifiedLogin,
    0x527d22eb: types.EmailVerifyPurposeLoginChange,
    0x4345be73: types.EmailVerifyPurposeLoginSetup,
    0xbbf51685: types.EmailVerifyPurposePassport,
    0x7a9abda9: types.EmojiGroup,
    0x80d26cc7: types.EmojiGroupGreeting,
    0x093bcf34: types.EmojiGroupPremium,
    0x881fb94b: types.messages.EmojiGroups,
    0x6fb4ad87: types.messages.EmojiGroupsNotModified,
    0xd5b3b9f9: types.EmojiKeyword,
    0x236df622: types.EmojiKeywordDeleted,
    0x5cc761bd: types.EmojiKeywordsDifference,
    0xb3fb5361: types.EmojiLanguage,
    0x7a1e11d1: types.EmojiList,
    0x481eadfa: types.EmojiListNotModified,
    0xe7ff068a: types.EmojiStatus,
    0x7184603b: types.EmojiStatusCollectible,
    0x2de11aae: types.EmojiStatusEmpty,
    0x90c467d1: types.account.EmojiStatuses,
    0xd08ce645: types.account.EmojiStatusesNotModified,
    0xa575739d: types.EmojiURL,
    0x61f0d4c7: types.EncryptedChat,
    0x1e1c7c45: types.EncryptedChatDiscarded,
    0xab7ec0a0: types.EncryptedChatEmpty,
    0x48f1d94c: types.EncryptedChatRequested,
    0x66b25953: types.EncryptedChatWaiting,
    0xa8008cd8: types.EncryptedFile,
    0xc21f497e: types.EncryptedFileEmpty,
    0xed18c118: types.EncryptedMessage,
    0x23734b06: types.EncryptedMessageService,
    0xe5bfffcd: functions.auth.ExportAuthorizationRequest,
    0xa455de90: functions.messages.ExportChatInviteRequest,
    0x8472478e: functions.chatlists.ExportChatlistInviteRequest,
    0xf8654027: functions.contacts.ExportContactTokenRequest,
    0xe6aa647f: functions.phone.ExportGroupCallInviteRequest,
    0x0f91b065: functions.payments.ExportInvoiceRequest,
    0xb7e085fe: functions.auth.ExportLoginTokenRequest,
    0xe63fadeb: functions.channels.ExportMessageLinkRequest,
    0x7b8def20: functions.stories.ExportStoryLinkRequest,
    0xb434e2b8: types.auth.ExportedAuthorization,
    0x1871be50: types.messages.ExportedChatInvite,
    0x222600ef: types.messages.ExportedChatInviteReplaced,
    0xbdc62dcc: types.messages.ExportedChatInvites,
    0x0c5181ac: types.ExportedChatlistInvite,
    0x10e6e3a6: types.chatlists.ExportedChatlistInvite,
    0x41bf109b: types.ExportedContactToken,
    0x204bd158: types.phone.ExportedGroupCallInvite,
    0x10ab6dc7: types.chatlists.ExportedInvites,
    0xaed0cbd9: types.payments.ExportedInvoice,
    0x5dab1af4: types.ExportedMessageLink,
    0x3fc9053b: types.ExportedStoryLink,
    0xb89bfccf: types.FactCheck,
    0xb9ffc55b: functions.messages.FaveStickerRequest,
    0x2cb51097: types.messages.FavedStickers,
    0x9e8fa6d3: types.messages.FavedStickersNotModified,
    0xbe382906: types.messages.FeaturedStickers,
    0xc6dc0c66: types.messages.FeaturedStickersNotModified,
    0x096a18d5: types.upload.File,
    0xf18cda44: types.upload.FileCdnRedirect,
    0xcae1aadf: types.storage.FileGif,
    0xf39b035c: types.FileHash,
    0x007efe0e: types.storage.FileJpeg,
    0x4b09ebbc: types.storage.FileMov,
    0x528a0677: types.storage.FileMp3,
    0xb3cea0e4: types.storage.FileMp4,
    0x40bc6f52: types.storage.FilePartial,
    0xae1e508d: types.storage.FilePdf,
    0x0a4f63c0: types.storage.FilePng,
    0xaa963b05: types.storage.FileUnknown,
    0x1081464c: types.storage.FileWebp,
    0x4f1ebf24: functions.smsjobs.FinishJobRequest,
    0x1d2652ee: functions.account.FinishTakeoutSessionRequest,
    0xff544e65: types.Folder,
    0xe9baa668: types.FolderPeer,
    0x71701da9: types.ForumTopic,
    0x023f109b: types.ForumTopicDeleted,
    0x367617d3: types.messages.ForumTopics,
    0xbb9fa475: functions.messages.ForwardMessagesRequest,
    0xb3134d9d: types.contacts.Found,
    0x8af09dd2: types.messages.FoundStickerSets,
    0x0d54b65d: types.messages.FoundStickerSetsNotModified,
    0x82c9e290: types.messages.FoundStickers,
    0x6010c534: types.messages.FoundStickersNotModified,
    0xe2de7737: types.stories.FoundStories,
    0xe87acbc0: types.FoundStory,
    0xcc5bebb3: functions.payments.FulfillStarsSubscriptionRequest,
    0x0949d9dc: types.FutureSalt,
    0xae500895: types.FutureSalts,
    0xbdf9653b: types.Game,
    0xb2a2f663: types.GeoPoint,
    0xde4c5d93: types.GeoPointAddress,
    0x1117dd5f: types.GeoPointEmpty,
    0x08fc711d: functions.account.GetAccountTTLRequest,
    0x33ddf480: functions.channels.GetAdminLogRequest,
    0xb0711d83: functions.bots.GetAdminedBotsRequest,
    0xf8b036af: functions.channels.GetAdminedPublicChannelsRequest,
    0x3920e6ef: functions.messages.GetAdminsWithInvitesRequest,
    0x6a3f8d65: functions.messages.GetAllDraftsRequest,
    0x9b5ae7f9: functions.stories.GetAllReadPeerStoriesRequest,
    0xb288bc7d: functions.account.GetAllSecureValuesRequest,
    0xb8a0a1a8: functions.messages.GetAllStickersRequest,
    0xeeb0d625: functions.stories.GetAllStoriesRequest,
    0x61e3f854: functions.help.GetAppConfigRequest,
    0x522d5a7d: functions.help.GetAppUpdateRequest,
    0x57f17692: functions.messages.GetArchivedStickersRequest,
    0x77216192: functions.messages.GetAttachMenuBotRequest,
    0x16fcc2cb: functions.messages.GetAttachMenuBotsRequest,
    0xcc5b67cc: functions.messages.GetAttachedStickersRequest,
    0xa929597a: functions.account.GetAuthorizationFormRequest,
    0xe320c158: functions.account.GetAuthorizationsRequest,
    0x56da0b3f: functions.account.GetAutoDownloadSettingsRequest,
    0xadcbbcda: functions.account.GetAutoSaveSettingsRequest,
    0xdea20a39: functions.messages.GetAvailableEffectsRequest,
    0x18dea0ac: functions.messages.GetAvailableReactionsRequest,
    0x2e79d779: functions.payments.GetBankCardDataRequest,
    0xdaeda864: functions.contacts.GetBirthdaysRequest,
    0x9a868f80: functions.contacts.GetBlockedRequest,
    0x60f67660: functions.premium.GetBoostsListRequest,
    0x042f1f61: functions.premium.GetBoostsStatusRequest,
    0x34fdc5c3: functions.messages.GetBotAppRequest,
    0x76a86270: functions.account.GetBotBusinessConnectionRequest,
    0x9342ca07: functions.messages.GetBotCallbackAnswerRequest,
    0xe34c0dd6: functions.bots.GetBotCommandsRequest,
    0xdcd914fd: functions.bots.GetBotInfoRequest,
    0x9c60eb28: functions.bots.GetBotMenuButtonRequest,
    0xa1b70815: functions.bots.GetBotRecommendationsRequest,
    0xf788ee19: functions.stats.GetBroadcastRevenueStatsRequest,
    0x70990b6d: functions.stats.GetBroadcastRevenueTransactionsRequest,
    0x9df4faad: functions.stats.GetBroadcastRevenueWithdrawalUrlRequest,
    0xab42441a: functions.stats.GetBroadcastStatsRequest,
    0x6f70dde1: functions.account.GetBusinessChatLinksRequest,
    0x55451fa9: functions.phone.GetCallConfigRequest,
    0x52029342: functions.help.GetCdnConfigRequest,
    0x395f69da: functions.upload.GetCdnFileRequest,
    0x91dc3f31: functions.upload.GetCdnFileHashesRequest,
    0x7727a7d5: functions.account.GetChannelDefaultEmojiStatusesRequest,
    0x03173d78: functions.updates.GetChannelDifferenceRequest,
    0x25a71742: functions.channels.GetChannelRecommendationsRequest,
    0x35a9e0d5: functions.account.GetChannelRestrictedStatusEmojisRequest,
    0x0a7f6bbb: functions.channels.GetChannelsRequest,
    0xdf04dd4e: functions.messages.GetChatInviteImportersRequest,
    0xd638de89: functions.account.GetChatThemesRequest,
    0x89419521: functions.chatlists.GetChatlistUpdatesRequest,
    0x49e9528f: functions.messages.GetChatsRequest,
    0xa56a8b60: functions.stories.GetChatsToSendRequest,
    0x2e7b4543: functions.account.GetCollectibleEmojiStatusesRequest,
    0xbe1e85ba: functions.fragment.GetCollectibleInfoRequest,
    0xe40ca104: functions.messages.GetCommonChatsRequest,
    0xc4f9186b: functions.help.GetConfigRequest,
    0x4ea4c80f: functions.account.GetConnectedBotsRequest,
    0xb7d998f0: functions.payments.GetConnectedStarRefBotRequest,
    0x5869a553: functions.payments.GetConnectedStarRefBotsRequest,
    0x7adc669d: functions.contacts.GetContactIDsRequest,
    0x9f07c728: functions.account.GetContactSignUpNotificationRequest,
    0x5dd69e12: functions.contacts.GetContactsRequest,
    0x8b9b4dae: functions.account.GetContentSettingsRequest,
    0x735787a8: functions.help.GetCountriesListRequest,
    0xd9ab0f54: functions.messages.GetCustomEmojiDocumentsRequest,
    0x3fedc75f: functions.help.GetDeepLinkInfoRequest,
    0xa60ab9ce: functions.account.GetDefaultBackgroundEmojisRequest,
    0xd6753386: functions.account.GetDefaultEmojiStatusesRequest,
    0x915860ae: functions.account.GetDefaultGroupPhotoEmojisRequest,
    0x658b7188: functions.messages.GetDefaultHistoryTTLRequest,
    0xe2750328: functions.account.GetDefaultProfilePhotoEmojisRequest,
    0xbdf93428: functions.messages.GetDefaultTagReactionsRequest,
    0x26cf8950: functions.messages.GetDhConfigRequest,
    0xefd48c89: functions.messages.GetDialogFiltersRequest,
    0x22e24e22: functions.messages.GetDialogUnreadMarksRequest,
    0xa0f4cb4f: functions.messages.GetDialogsRequest,
    0x19c2f763: functions.updates.GetDifferenceRequest,
    0xcd984aa5: functions.langpack.GetDifferenceRequest,
    0x446972fd: functions.messages.GetDiscussionMessageRequest,
    0xb1f2061f: functions.messages.GetDocumentByHashRequest,
    0x7488ce5b: functions.messages.GetEmojiGroupsRequest,
    0x35a0e062: functions.messages.GetEmojiKeywordsRequest,
    0x1508b6af: functions.messages.GetEmojiKeywordsDifferenceRequest,
    0x4e9963b2: functions.messages.GetEmojiKeywordsLanguagesRequest,
    0x21a548f3: functions.messages.GetEmojiProfilePhotoGroupsRequest,
    0x2ecd56cd: functions.messages.GetEmojiStatusGroupsRequest,
    0x1dd840f5: functions.messages.GetEmojiStickerGroupsRequest,
    0xfbfca18f: functions.messages.GetEmojiStickersRequest,
    0xd5b10c26: functions.messages.GetEmojiURLRequest,
    0x73746f5c: functions.messages.GetExportedChatInviteRequest,
    0xa2b5a3f6: functions.messages.GetExportedChatInvitesRequest,
    0xce03da83: functions.chatlists.GetExportedInvitesRequest,
    0x84f80814: functions.messages.GetExtendedMediaRequest,
    0xb9cdc5ee: functions.messages.GetFactCheckRequest,
    0x04f1aaa9: functions.messages.GetFavedStickersRequest,
    0x0ecf6736: functions.messages.GetFeaturedEmojiStickersRequest,
    0x64780b14: functions.messages.GetFeaturedStickersRequest,
    0xbe5335be: functions.upload.GetFileRequest,
    0x9156982a: functions.upload.GetFileHashesRequest,
    0x0de560d1: functions.channels.GetForumTopicsRequest,
    0xb0831eb9: functions.channels.GetForumTopicsByIDRequest,
    0x08736a09: functions.channels.GetFullChannelRequest,
    0xaeb00b34: functions.messages.GetFullChatRequest,
    0xb60f5918: functions.users.GetFullUserRequest,
    0xe822649d: functions.messages.GetGameHighScoresRequest,
    0xf4239425: functions.payments.GetGiveawayInfoRequest,
    0xeb2b4cf6: functions.account.GetGlobalPrivacySettingsRequest,
    0x041845db: functions.phone.GetGroupCallRequest,
    0xef7c213a: functions.phone.GetGroupCallJoinAsRequest,
    0x1ab21940: functions.phone.GetGroupCallStreamChannelsRequest,
    0xdeb3abbf: functions.phone.GetGroupCallStreamRtmpUrlRequest,
    0xc558d8ab: functions.phone.GetGroupParticipantsRequest,
    0xf5dad378: functions.channels.GetGroupsForDiscussionRequest,
    0x4423e6c5: functions.messages.GetHistoryRequest,
    0x11e831ee: functions.channels.GetInactiveChannelsRequest,
    0x514e999d: functions.messages.GetInlineBotResultsRequest,
    0x0f635e1b: functions.messages.GetInlineGameHighScoresRequest,
    0x4d392343: functions.help.GetInviteTextRequest,
    0xf2f2330a: functions.langpack.GetLangPackRequest,
    0x6a596502: functions.langpack.GetLanguageRequest,
    0x42c6978f: functions.langpack.GetLanguagesRequest,
    0xfdbcd714: functions.chatlists.GetLeaveChatlistSuggestionsRequest,
    0x8341ecc0: functions.channels.GetLeftChannelsRequest,
    0xd348bc44: functions.contacts.GetLocatedRequest,
    0x640f82b8: functions.messages.GetMaskStickersRequest,
    0xdcdf8607: functions.stats.GetMegagroupStatsRequest,
    0xfda68d36: functions.messages.GetMessageEditDataRequest,
    0x5f150144: functions.stats.GetMessagePublicForwardsRequest,
    0x461b3f48: functions.messages.GetMessageReactionsListRequest,
    0x31c1c44f: functions.messages.GetMessageReadParticipantsRequest,
    0xb6e0a3f5: functions.stats.GetMessageStatsRequest,
    0x63c66506: functions.messages.GetMessagesRequest,
    0xad8c9a23: functions.channels.GetMessagesRequest,
    0x8bba90e6: functions.messages.GetMessagesReactionsRequest,
    0x5784d3e1: functions.messages.GetMessagesViewsRequest,
    0x65ad71dc: functions.account.GetMultiWallPapersRequest,
    0x0be77b4a: functions.premium.GetMyBoostsRequest,
    0xd0b5e1fc: functions.messages.GetMyStickersRequest,
    0x1fb33026: functions.help.GetNearestDcRequest,
    0x53577479: functions.account.GetNotifyExceptionsRequest,
    0x12b3ad31: functions.account.GetNotifySettingsRequest,
    0x7ed094a1: functions.messages.GetOldFeaturedStickersRequest,
    0x6e2be050: functions.messages.GetOnlinesRequest,
    0x8c4bfe5d: functions.messages.GetOutboxReadDateRequest,
    0xf1266f38: functions.account.GetPaidMessagesRevenueRequest,
    0x472455aa: functions.messages.GetPaidReactionPrivacyRequest,
    0xa0ab6cc6: functions.channels.GetParticipantRequest,
    0x77ced9d0: functions.channels.GetParticipantsRequest,
    0xc661ad08: functions.help.GetPassportConfigRequest,
    0x548a30f5: functions.account.GetPasswordRequest,
    0x9cd4eaf9: functions.account.GetPasswordSettingsRequest,
    0x37148dbb: functions.payments.GetPaymentFormRequest,
    0x2478d1cc: functions.payments.GetPaymentReceiptRequest,
    0xda80f42f: functions.help.GetPeerColorsRequest,
    0xe470bcfd: functions.messages.GetPeerDialogsRequest,
    0x535983c3: functions.stories.GetPeerMaxIDsRequest,
    0xabcfa9fd: functions.help.GetPeerProfileColorsRequest,
    0xefd9a6a2: functions.messages.GetPeerSettingsRequest,
    0x2c4ada50: functions.stories.GetPeerStoriesRequest,
    0xd6b94df2: functions.messages.GetPinnedDialogsRequest,
    0xd63d94e0: functions.messages.GetPinnedSavedDialogsRequest,
    0x5821a5dc: functions.stories.GetPinnedStoriesRequest,
    0x73bb643b: functions.messages.GetPollResultsRequest,
    0xb86e380e: functions.messages.GetPollVotesRequest,
    0xc2510192: functions.bots.GetPopularAppBotsRequest,
    0x2757ba54: functions.payments.GetPremiumGiftCodeOptionsRequest,
    0xb81b93d4: functions.help.GetPremiumPromoRequest,
    0x857ebdb8: functions.messages.GetPreparedInlineMessageRequest,
    0x423ab3ad: functions.bots.GetPreviewInfoRequest,
    0xa2a5594d: functions.bots.GetPreviewMediasRequest,
    0xdadbc950: functions.account.GetPrivacyRequest,
    0xc0977421: functions.help.GetPromoDataRequest,
    0xd483f2a8: functions.messages.GetQuickRepliesRequest,
    0x94a495c3: functions.messages.GetQuickReplyMessagesRequest,
    0x06dd654c: functions.account.GetReactionsNotifySettingsRequest,
    0x0f578105: functions.account.GetRecentEmojiStatusesRequest,
    0x702a40e0: functions.messages.GetRecentLocationsRequest,
    0x3dc0f114: functions.help.GetRecentMeUrlsRequest,
    0x39461db2: functions.messages.GetRecentReactionsRequest,
    0x9da9403b: functions.messages.GetRecentStickersRequest,
    0x22ddd30c: functions.messages.GetRepliesRequest,
    0xd89a83a3: functions.users.GetRequirementsToContactRequest,
    0x82f1e39f: functions.contacts.GetSavedRequest,
    0x5381d21a: functions.messages.GetSavedDialogsRequest,
    0x5cf09635: functions.messages.GetSavedGifsRequest,
    0x3d9a414d: functions.messages.GetSavedHistoryRequest,
    0x227d824b: functions.payments.GetSavedInfoRequest,
    0x3637e05b: functions.messages.GetSavedReactionTagsRequest,
    0xe1902288: functions.account.GetSavedRingtonesRequest,
    0xb455a106: functions.payments.GetSavedStarGiftRequest,
    0x23830de9: functions.payments.GetSavedStarGiftsRequest,
    0xf516760b: functions.messages.GetScheduledHistoryRequest,
    0xbdbb0464: functions.messages.GetScheduledMessagesRequest,
    0x1bbcf300: functions.messages.GetSearchCountersRequest,
    0x6aa3f6bd: functions.messages.GetSearchResultsCalendarRequest,
    0x9c7f2f10: functions.messages.GetSearchResultsPositionsRequest,
    0x73665bc2: functions.account.GetSecureValueRequest,
    0xe785a43f: functions.channels.GetSendAsRequest,
    0x778d902f: functions.smsjobs.GetSmsJobRequest,
    0x1cff7e08: functions.messages.GetSplitRangesRequest,
    0x9bd2f439: functions.messages.GetSponsoredMessagesRequest,
    0xb6c8c393: functions.contacts.GetSponsoredPeersRequest,
    0x9c9abcb1: functions.payments.GetStarGiftUpgradePreviewRequest,
    0xd06e93a8: functions.payments.GetStarGiftWithdrawalUrlRequest,
    0xc4563590: functions.payments.GetStarGiftsRequest,
    0xd3c96bc8: functions.payments.GetStarsGiftOptionsRequest,
    0xbd1efd3e: functions.payments.GetStarsGiveawayOptionsRequest,
    0xd1d7efc5: functions.payments.GetStarsRevenueAdsAccountUrlRequest,
    0xd91ffad6: functions.payments.GetStarsRevenueStatsRequest,
    0x13bbe8b3: functions.payments.GetStarsRevenueWithdrawalUrlRequest,
    0x104fcfa7: functions.payments.GetStarsStatusRequest,
    0x032512c5: functions.payments.GetStarsSubscriptionsRequest,
    0xc00ec7d3: functions.payments.GetStarsTopupOptionsRequest,
    0x69da4557: functions.payments.GetStarsTransactionsRequest,
    0x27842d2e: functions.payments.GetStarsTransactionsByIDRequest,
    0xedd4882a: functions.updates.GetStateRequest,
    0x10a698e8: functions.smsjobs.GetStatusRequest,
    0xc4a353ee: functions.contacts.GetStatusesRequest,
    0xc8a0ec74: functions.messages.GetStickerSetRequest,
    0xd5a5d3a1: functions.messages.GetStickersRequest,
    0xb4352016: functions.stories.GetStoriesArchiveRequest,
    0x5774ca74: functions.stories.GetStoriesByIDRequest,
    0x28e16cc8: functions.stories.GetStoriesViewsRequest,
    0xa6437ef6: functions.stats.GetStoryPublicForwardsRequest,
    0xb9b2881f: functions.stories.GetStoryReactionsListRequest,
    0x374fef40: functions.stats.GetStoryStatsRequest,
    0x7ed23c57: functions.stories.GetStoryViewsListRequest,
    0xefea3803: functions.langpack.GetStringsRequest,
    0xa29cd42c: functions.messages.GetSuggestedDialogFiltersRequest,
    0x0d6b48f7: functions.payments.GetSuggestedStarRefBotsRequest,
    0x9cdf08cd: functions.help.GetSupportRequest,
    0xd360e72c: functions.help.GetSupportNameRequest,
    0x2ca51fd1: functions.help.GetTermsOfServiceUpdateRequest,
    0x3a5869ec: functions.account.GetThemeRequest,
    0x7206e458: functions.account.GetThemesRequest,
    0x49b30240: functions.help.GetTimezonesListRequest,
    0x449e0b51: functions.account.GetTmpPasswordRequest,
    0x973478b6: functions.contacts.GetTopPeersRequest,
    0xbb8125ba: functions.messages.GetTopReactionsRequest,
    0xa1974d72: functions.payments.GetUniqueStarGiftRequest,
    0xf107e790: functions.messages.GetUnreadMentionsRequest,
    0x3223495b: functions.messages.GetUnreadReactionsRequest,
    0x39854d1f: functions.premium.GetUserBoostsRequest,
    0x038a08d3: functions.help.GetUserInfoRequest,
    0x91cd32a8: functions.photos.GetUserPhotosRequest,
    0x0d91a548: functions.users.GetUsersRequest,
    0xfc8ddbea: functions.account.GetWallPaperRequest,
    0x07967d36: functions.account.GetWallPapersRequest,
    0x182e6d6f: functions.account.GetWebAuthorizationsRequest,
    0x24e6818d: functions.upload.GetWebFileRequest,
    0x8d9692a3: functions.messages.GetWebPageRequest,
    0x570d6f6f: functions.messages.GetWebPagePreviewRequest,
    0xb921bd04: functions.GetFutureSaltsRequest,
    0x4367daa0: types.payments.GiveawayInfo,
    0xe175e66f: types.payments.GiveawayInfoResults,
    0xfe41b34f: types.GlobalPrivacySettings,
    0xcdf8d3e3: types.GroupCall,
    0x9e727aad: types.phone.GroupCall,
    0x7780bcb4: types.GroupCallDiscarded,
    0xeba636fe: types.GroupCallParticipant,
    0x67753ac8: types.GroupCallParticipantVideo,
    0xdcb118b7: types.GroupCallParticipantVideoSourceGroup,
    0x80eb48af: types.GroupCallStreamChannel,
    0xd0e482b2: types.phone.GroupCallStreamChannels,
    0x2dbf3432: types.phone.GroupCallStreamRtmpUrl,
    0xf47751b6: types.phone.GroupParticipants,
    0xe085f4ea: functions.messages.HideAllChatJoinRequestsRequest,
    0x7fe7e815: functions.messages.HideChatJoinRequestRequest,
    0x66e486fb: functions.chatlists.HideChatlistUpdatesRequest,
    0x4facb138: functions.messages.HidePeerSettingsBarRequest,
    0x1e251c95: functions.help.HidePromoDataRequest,
    0x73a379eb: types.HighScore,
    0x9a3bfd99: types.messages.HighScores,
    0x1662af0b: types.messages.HistoryImport,
    0x5e0fb7b9: types.messages.HistoryImportParsed,
    0x9299359f: types.HttpWait,
    0xa57a7dad: functions.auth.ImportAuthorizationRequest,
    0x67a3ff2c: functions.auth.ImportBotAuthorizationRequest,
    0x6c50051c: functions.messages.ImportChatInviteRequest,
    0x13005788: functions.contacts.ImportContactTokenRequest,
    0x2c800be5: functions.contacts.ImportContactsRequest,
    0x95ac5ce4: functions.auth.ImportLoginTokenRequest,
    0x2db873a9: functions.auth.ImportWebTokenAuthorizationRequest,
    0xc13e3c50: types.ImportedContact,
    0x77d01c3b: types.contacts.ImportedContacts,
    0xa927fec5: types.messages.InactiveChats,
    0xb2028afb: functions.stories.IncrementStoryViewsRequest,
    0xc1cd5ea9: functions.InitConnectionRequest,
    0x34090c3b: functions.messages.InitHistoryImportRequest,
    0x8ef3eab0: functions.account.InitTakeoutSessionRequest,
    0x3c20629f: types.InlineBotSwitchPM,
    0xb57295d5: types.InlineBotWebView,
    0x0e3b2d0c: types.InlineQueryPeerTypeBotPM,
    0x6334ee9a: types.InlineQueryPeerTypeBroadcast,
    0xd766c50a: types.InlineQueryPeerTypeChat,
    0x5ec4be43: types.InlineQueryPeerTypeMegagroup,
    0x833c0fac: types.InlineQueryPeerTypePM,
    0x3081ed9d: types.InlineQueryPeerTypeSameBotPM,
    0x1d1b1245: types.InputAppEvent,
    0xa920bd7a: types.InputBotAppID,
    0x908c0407: types.InputBotAppShortName,
    0x4b425864: types.InputBotInlineMessageGame,
    0x890c3d89: types.InputBotInlineMessageID,
    0xb6d915d7: types.InputBotInlineMessageID64,
    0x3380c786: types.InputBotInlineMessageMediaAuto,
    0xa6edbffd: types.InputBotInlineMessageMediaContact,
    0x96929a85: types.InputBotInlineMessageMediaGeo,
    0xd7e78225: types.InputBotInlineMessageMediaInvoice,
    0x417bbf11: types.InputBotInlineMessageMediaVenue,
    0xbddcc510: types.InputBotInlineMessageMediaWebPage,
    0x3dcd7a87: types.InputBotInlineMessageText,
    0x88bf9319: types.InputBotInlineResult,
    0xfff8fdc4: types.InputBotInlineResultDocument,
    0x4fa417f2: types.InputBotInlineResultGame,
    0xa8d864a7: types.InputBotInlineResultPhoto,
    0x832175e0: types.InputBusinessAwayMessage,
    0xc4e5921e: types.InputBusinessBotRecipients,
    0x11679fa7: types.InputBusinessChatLink,
    0x0194cb3b: types.InputBusinessGreetingMessage,
    0x09c469cd: types.InputBusinessIntro,
    0x6f8b32aa: types.InputBusinessRecipients,
    0xf35aec28: types.InputChannel,
    0xee8c1e86: types.InputChannelEmpty,
    0x5b934f9d: types.InputChannelFromMessage,
    0x8953ad37: types.InputChatPhoto,
    0x1ca48f57: types.InputChatPhotoEmpty,
    0xbdcdaec0: types.InputChatUploadedPhoto,
    0xf3e0da33: types.InputChatlistDialogFilter,
    0x9880f658: types.InputCheckPasswordEmpty,
    0xd27ff082: types.InputCheckPasswordSRP,
    0x75588b3f: types.InputClientProxy,
    0xa2e214a4: types.InputCollectiblePhone,
    0xe39460a9: types.InputCollectibleUsername,
    0xfcaafeb7: types.InputDialogPeer,
    0x64600527: types.InputDialogPeerFolder,
    0x1abfb575: types.InputDocument,
    0x72f0eaae: types.InputDocumentEmpty,
    0xbad07584: types.InputDocumentFileLocation,
    0x07141dbf: types.InputEmojiStatusCollectible,
    0xf141b5e1: types.InputEncryptedChat,
    0x5a17b5e5: types.InputEncryptedFile,
    0x2dc173c8: types.InputEncryptedFileBigUploaded,
    0x1837c364: types.InputEncryptedFileEmpty,
    0xf5235d55: types.InputEncryptedFileLocation,
    0x64bd0306: types.InputEncryptedFileUploaded,
    0xf52ff27f: types.InputFile,
    0xfa4f0bb5: types.InputFileBig,
    0xdfdaabe1: types.InputFileLocation,
    0x62dc8b48: types.InputFileStoryDocument,
    0xfbd2c296: types.InputFolderPeer,
    0x032c3e77: types.InputGameID,
    0xc331e80a: types.InputGameShortName,
    0x48222faf: types.InputGeoPoint,
    0xe4c123d6: types.InputGeoPointEmpty,
    0xd8aa840f: types.InputGroupCall,
    0x0598a92a: types.InputGroupCallStream,
    0xf4997e42: types.InputInvoiceBusinessBotTransferStars,
    0x34e793f1: types.InputInvoiceChatInviteSubscription,
    0xc5b56859: types.InputInvoiceMessage,
    0x98986c0d: types.InputInvoicePremiumGiftCode,
    0xdabab2ef: types.InputInvoicePremiumGiftStars,
    0xc326caef: types.InputInvoiceSlug,
    0xe8625e92: types.InputInvoiceStarGift,
    0x4a5f5bd9: types.InputInvoiceStarGiftTransfer,
    0x4d818d5d: types.InputInvoiceStarGiftUpgrade,
    0x65f00ce3: types.InputInvoiceStars,
    0xc9662d05: types.InputKeyboardButtonRequestPeer,
    0xd02e7fd4: types.InputKeyboardButtonUrlAuth,
    0xe988037b: types.InputKeyboardButtonUserProfile,
    0x2271f2bf: types.InputMediaAreaChannelPost,
    0xb282217f: types.InputMediaAreaVenue,
    0xf8ab7dfb: types.InputMediaContact,
    0xe66fbf7b: types.InputMediaDice,
    0xa8763ab5: types.InputMediaDocument,
    0x779600f9: types.InputMediaDocumentExternal,
    0x9664f57f: types.InputMediaEmpty,
    0xd33f43f3: types.InputMediaGame,
    0x971fa843: types.InputMediaGeoLive,
    0xf9c44144: types.InputMediaGeoPoint,
    0x405fef0d: types.InputMediaInvoice,
    0xc4103386: types.InputMediaPaidMedia,
    0xb3ba0635: types.InputMediaPhoto,
    0xe5bbfe1a: types.InputMediaPhotoExternal,
    0x0f94e5f1: types.InputMediaPoll,
    0x89fdd778: types.InputMediaStory,
    0x037c9330: types.InputMediaUploadedDocument,
    0x1e287d04: types.InputMediaUploadedPhoto,
    0xc13d1c11: types.InputMediaVenue,
    0xc21b8849: types.InputMediaWebPage,
    0xacfa1a7e: types.InputMessageCallbackQuery,
    0x208e68c9: types.InputMessageEntityMentionName,
    0xa676a322: types.InputMessageID,
    0x86872538: types.InputMessagePinned,
    0xbad88395: types.InputMessageReplyTo,
    0x3a20ecb8: types.InputMessagesFilterChatPhotos,
    0xe062db83: types.InputMessagesFilterContacts,
    0x9eddf188: types.InputMessagesFilterDocument,
    0x57e2f66c: types.InputMessagesFilterEmpty,
    0xe7026d0d: types.InputMessagesFilterGeo,
    0xffc86587: types.InputMessagesFilterGif,
    0x3751b49e: types.InputMessagesFilterMusic,
    0xc1f8e69a: types.InputMessagesFilterMyMentions,
    0x80c99768: types.InputMessagesFilterPhoneCalls,
    0x56e9f0e4: types.InputMessagesFilterPhotoVideo,
    0x9609a51c: types.InputMessagesFilterPhotos,
    0x1bb00451: types.InputMessagesFilterPinned,
    0xb549da53: types.InputMessagesFilterRoundVideo,
    0x7a7c17a4: types.InputMessagesFilterRoundVoice,
    0x7ef0dd87: types.InputMessagesFilterUrl,
    0x9fc00e65: types.InputMessagesFilterVideo,
    0x50f5c392: types.InputMessagesFilterVoice,
    0xb1db7c7e: types.InputNotifyBroadcasts,
    0x4a95e84e: types.InputNotifyChats,
    0x5c467992: types.InputNotifyForumTopic,
    0xb8bc5b0c: types.InputNotifyPeer,
    0x193b4417: types.InputNotifyUsers,
    0x3417d728: types.InputPaymentCredentials,
    0x0aa1c39f: types.InputPaymentCredentialsApplePay,
    0x8ac32801: types.InputPaymentCredentialsGooglePay,
    0xc10eb2cf: types.InputPaymentCredentialsSaved,
    0x27bcbbfc: types.InputPeerChannel,
    0xbd2a0840: types.InputPeerChannelFromMessage,
    0x35a95cb9: types.InputPeerChat,
    0x7f3b18ea: types.InputPeerEmpty,
    0xcacb6ae2: types.InputPeerNotifySettings,
    0x37257e99: types.InputPeerPhotoFileLocation,
    0x7da07ec9: types.InputPeerSelf,
    0xdde8a54c: types.InputPeerUser,
    0xa87b0a1c: types.InputPeerUserFromMessage,
    0x1e36fded: types.InputPhoneCall,
    0xf392b7f4: types.InputPhoneContact,
    0x3bb3b94a: types.InputPhoto,
    0x1cd7bf0d: types.InputPhotoEmpty,
    0x40181ffe: types.InputPhotoFileLocation,
    0xd83466f3: types.InputPhotoLegacyFileLocation,
    0x3823cc40: types.InputPrivacyKeyAbout,
    0xd1219bdd: types.InputPrivacyKeyAddedByPhone,
    0xd65a11cc: types.InputPrivacyKeyBirthday,
    0xbdfb0426: types.InputPrivacyKeyChatInvite,
    0xa4dd4c08: types.InputPrivacyKeyForwards,
    0xbdc597b4: types.InputPrivacyKeyNoPaidMessages,
    0xfabadc5f: types.InputPrivacyKeyPhoneCall,
    0x0352dafa: types.InputPrivacyKeyPhoneNumber,
    0xdb9e70d2: types.InputPrivacyKeyPhoneP2P,
    0x5719bacc: types.InputPrivacyKeyProfilePhoto,
    0xe1732341: types.InputPrivacyKeyStarGiftsAutoSave,
    0x4f96cb18: types.InputPrivacyKeyStatusTimestamp,
    0xaee69d68: types.InputPrivacyKeyVoiceMessages,
    0x184b35ce: types.InputPrivacyValueAllowAll,
    0x5a4fcce5: types.InputPrivacyValueAllowBots,
    0x840649cf: types.InputPrivacyValueAllowChatParticipants,
    0x2f453e49: types.InputPrivacyValueAllowCloseFriends,
    0x0d09e07b: types.InputPrivacyValueAllowContacts,
    0x77cdc9f1: types.InputPrivacyValueAllowPremium,
    0x131cc67f: types.InputPrivacyValueAllowUsers,
    0xd66b66c9: types.InputPrivacyValueDisallowAll,
    0xc4e57915: types.InputPrivacyValueDisallowBots,
    0xe94f0f86: types.InputPrivacyValueDisallowChatParticipants,
    0x0ba52007: types.InputPrivacyValueDisallowContacts,
    0x90110467: types.InputPrivacyValueDisallowUsers,
    0x24596d41: types.InputQuickReplyShortcut,
    0x01190cf1: types.InputQuickReplyShortcutId,
    0x22c0f6d5: types.InputReplyToMessage,
    0x5881323a: types.InputReplyToStory,
    0xadf44ee3: types.InputReportReasonChildAbuse,
    0x9b89f93a: types.InputReportReasonCopyright,
    0xf5ddd6e7: types.InputReportReasonFake,
    0xdbd4feed: types.InputReportReasonGeoIrrelevant,
    0x0a8eb2be: types.InputReportReasonIllegalDrugs,
    0xc1e4a2b1: types.InputReportReasonOther,
    0x9ec7863d: types.InputReportReasonPersonalDetails,
    0x2e59d922: types.InputReportReasonPornography,
    0x58dbcab8: types.InputReportReasonSpam,
    0x1e22c78d: types.InputReportReasonViolence,
    0xf101aa7f: types.InputSavedStarGiftChat,
    0x69279795: types.InputSavedStarGiftUser,
    0x5367e5be: types.InputSecureFile,
    0xcbc7ee28: types.InputSecureFileLocation,
    0x3334b0f0: types.InputSecureFileUploaded,
    0xdb21d0a7: types.InputSecureValue,
    0x1cc6e91f: types.InputSingleMedia,
    0x206ae6d1: types.InputStarsTransaction,
    0x028703c8: types.InputStickerSetAnimatedEmoji,
    0x0cde3739: types.InputStickerSetAnimatedEmojiAnimations,
    0xe67f520e: types.InputStickerSetDice,
    0x49748553: types.InputStickerSetEmojiChannelDefaultStatuses,
    0x29d0f5ee: types.InputStickerSetEmojiDefaultStatuses,
    0x44c1f8e9: types.InputStickerSetEmojiDefaultTopicIcons,
    0x04c4d4ce: types.InputStickerSetEmojiGenericAnimations,
    0xffb62b95: types.InputStickerSetEmpty,
    0x9de7a269: types.InputStickerSetID,
    0x32da9e9c: types.InputStickerSetItem,
    0xc88b3b02: types.InputStickerSetPremiumGifts,
    0x861cc8a0: types.InputStickerSetShortName,
    0x9d84f3db: types.InputStickerSetThumb,
    0x0438865b: types.InputStickeredMediaDocument,
    0x4a992157: types.InputStickeredMediaPhoto,
    0x9bb2636d: types.InputStorePaymentAuthCode,
    0x616f7fe8: types.InputStorePaymentGiftPremium,
    0xfb790393: types.InputStorePaymentPremiumGiftCode,
    0x160544ca: types.InputStorePaymentPremiumGiveaway,
    0xa6751e66: types.InputStorePaymentPremiumSubscription,
    0x1d741ef7: types.InputStorePaymentStarsGift,
    0x751f08fa: types.InputStorePaymentStarsGiveaway,
    0xdddd0f56: types.InputStorePaymentStarsTopup,
    0x29be5899: types.InputTakeoutFileLocation,
    0x3c5693e9: types.InputTheme,
    0x8fde504f: types.InputThemeSettings,
    0xf5890df1: types.InputThemeSlug,
    0xf21158c6: types.InputUser,
    0xb98886cf: types.InputUserEmpty,
    0x1da448e2: types.InputUserFromMessage,
    0xf7c1b13f: types.InputUserSelf,
    0xe630b979: types.InputWallPaper,
    0x967a462e: types.InputWallPaperNoFile,
    0x72091c80: types.InputWallPaperSlug,
    0x9bed434d: types.InputWebDocument,
    0xf46fe924: types.InputWebFileAudioAlbumThumbLocation,
    0x9f2221c9: types.InputWebFileGeoPointLocation,
    0xc239d686: types.InputWebFileLocation,
    0xc78fe460: functions.messages.InstallStickerSetRequest,
    0xc727bb3b: functions.account.InstallThemeRequest,
    0xfeed5769: functions.account.InstallWallPaperRequest,
    0xca8ae8ba: functions.account.InvalidateSignInCodesRequest,
    0x18cb9f78: types.help.InviteText,
    0xc9e33d54: functions.channels.InviteToChannelRequest,
    0x7b393160: functions.phone.InviteToGroupCallRequest,
    0x7f5defa6: types.messages.InvitedUsers,
    0x049ee584: types.Invoice,
    0xcb9f372d: functions.InvokeAfterMsgRequest,
    0x3dc4b4f0: functions.InvokeAfterMsgsRequest,
    0x087fc5e7: functions.bots.InvokeWebViewCustomMethodRequest,
    0x0dae54f8: functions.InvokeWithApnsSecretRequest,
    0xdd289f8e: functions.InvokeWithBusinessConnectionRequest,
    0x1df92984: functions.InvokeWithGooglePlayIntegrityRequest,
    0xda9b0d0d: functions.InvokeWithLayerRequest,
    0x365275f2: functions.InvokeWithMessagesRangeRequest,
    0xadbb0f94: functions.InvokeWithReCaptchaRequest,
    0xaca9fd2e: functions.InvokeWithTakeoutRequest,
    0xbf9459b7: functions.InvokeWithoutUpdatesRequest,
    0xd433ad73: types.IpPort,
    0x37982646: types.IpPortSecret,
    0x0edc39d0: functions.smsjobs.IsEligibleToJoinRequest,
    0xa74ece2d: functions.smsjobs.JoinRequest,
    0xafe5623f: types.phone.JoinAsPeers,
    0x24b524c5: functions.channels.JoinChannelRequest,
    0xa6b1e39a: functions.chatlists.JoinChatlistInviteRequest,
    0xe089f8f5: functions.chatlists.JoinChatlistUpdatesRequest,
    0xd61e1df3: functions.phone.JoinGroupCallRequest,
    0xcbea6bc4: functions.phone.JoinGroupCallPresentationRequest,
    0xf7444763: types.JsonArray,
    0xc7345e6a: types.JsonBool,
    0x3f6d7b68: types.JsonNull,
    0x2be0dfa4: types.JsonNumber,
    0x99c1d49d: types.JsonObject,
    0xc0de1bd9: types.JsonObjectValue,
    0xb71e767a: types.JsonString,
    0xa2fa4880: types.KeyboardButton,
    0xafd93fbb: types.KeyboardButtonBuy,
    0x35bbdb6b: types.KeyboardButtonCallback,
    0x75d2698e: types.KeyboardButtonCopy,
    0x50f41ccf: types.KeyboardButtonGame,
    0xfc796b3f: types.KeyboardButtonRequestGeoLocation,
    0x53d7bfd8: types.KeyboardButtonRequestPeer,
    0xb16a6c29: types.KeyboardButtonRequestPhone,
    0xbbc7515d: types.KeyboardButtonRequestPoll,
    0x77608b83: types.KeyboardButtonRow,
    0xa0c0505c: types.KeyboardButtonSimpleWebView,
    0x93b9fbb5: types.KeyboardButtonSwitchInline,
    0x258aff05: types.KeyboardButtonUrl,
    0x10b78d29: types.KeyboardButtonUrlAuth,
    0x308660c1: types.KeyboardButtonUserProfile,
    0x13767230: types.KeyboardButtonWebView,
    0xcb296bf8: types.LabeledPrice,
    0xf385c1f6: types.LangPackDifference,
    0xeeca5ce3: types.LangPackLanguage,
    0xcad181f6: types.LangPackString,
    0x2979eeb2: types.LangPackStringDeleted,
    0x6c47ac9f: types.LangPackStringPluralized,
    0x5ff58f20: functions.payments.LaunchPrepaidGiveawayRequest,
    0x9898ad73: functions.smsjobs.LeaveRequest,
    0xf836aa95: functions.channels.LeaveChannelRequest,
    0x74fae13a: functions.chatlists.LeaveChatlistRequest,
    0x500377f9: functions.phone.LeaveGroupCallRequest,
    0x1c50d144: functions.phone.LeaveGroupCallPresentationRequest,
    0x621d5fa0: functions.stats.LoadAsyncGraphRequest,
    0x3e72ba19: functions.auth.LogOutRequest,
    0xc3a2835f: types.auth.LoggedOut,
    0x629f1980: types.auth.LoginToken,
    0x068e9916: types.auth.LoginTokenMigrateTo,
    0x390d5c5e: types.auth.LoginTokenSuccess,
    0xc286d98f: functions.messages.MarkDialogUnreadRequest,
    0xaed6dbb2: types.MaskCoords,
    0x770416af: types.MediaAreaChannelPost,
    0xcfc9e002: types.MediaAreaCoordinates,
    0xcad5452d: types.MediaAreaGeoPoint,
    0x5787686d: types.MediaAreaStarGift,
    0x14455871: types.MediaAreaSuggestedReaction,
    0x37381085: types.MediaAreaUrl,
    0xbe82db9c: types.MediaAreaVenue,
    0x49a6549c: types.MediaAreaWeather,
    0xef7ff916: types.stats.MegagroupStats,
    0xeabcdd4d: types.Message,
    0xcc02aa6d: types.MessageActionBoostApply,
    0xc516d679: types.MessageActionBotAllowed,
    0x95d2ac92: types.MessageActionChannelCreate,
    0xea3948e9: types.MessageActionChannelMigrateFrom,
    0x15cefd00: types.MessageActionChatAddUser,
    0xbd47cbad: types.MessageActionChatCreate,
    0x95e3fbef: types.MessageActionChatDeletePhoto,
    0xa43f30cc: types.MessageActionChatDeleteUser,
    0x7fcb13a8: types.MessageActionChatEditPhoto,
    0xb5a1ce5a: types.MessageActionChatEditTitle,
    0x031224c3: types.MessageActionChatJoinedByLink,
    0xebbca3cb: types.MessageActionChatJoinedByRequest,
    0xe1037f92: types.MessageActionChatMigrateTo,
    0xf3f25f76: types.MessageActionContactSignUp,
    0xfae69f56: types.MessageActionCustomAction,
    0xb6aef7b0: types.MessageActionEmpty,
    0x92a72876: types.MessageActionGameScore,
    0x98e0d697: types.MessageActionGeoProximityReached,
    0x56d03994: types.MessageActionGiftCode,
    0x6c6274fa: types.MessageActionGiftPremium,
    0x45d5b021: types.MessageActionGiftStars,
    0xa80f51e4: types.MessageActionGiveawayLaunch,
    0x87e2f155: types.MessageActionGiveawayResults,
    0x7a0d7f42: types.MessageActionGroupCall,
    0xb3a07661: types.MessageActionGroupCallScheduled,
    0x9fbab604: types.MessageActionHistoryClear,
    0x502f92f7: types.MessageActionInviteToGroupCall,
    0xbcd71419: types.MessageActionPaidMessagesPrice,
    0xac1f1fcd: types.MessageActionPaidMessagesRefunded,
    0x41b3e202: types.MessageActionPaymentRefunded,
    0xc624b16e: types.MessageActionPaymentSent,
    0xffa00ccc: types.MessageActionPaymentSentMe,
    0x80e11a7f: types.MessageActionPhoneCall,
    0x94bd38ed: types.MessageActionPinMessage,
    0xb00c47a2: types.MessageActionPrizeStars,
    0x31518e9b: types.MessageActionRequestedPeer,
    0x93b31848: types.MessageActionRequestedPeerSentMe,
    0x4792929b: types.MessageActionScreenshotTaken,
    0xd95c6154: types.MessageActionSecureValuesSent,
    0x1b287353: types.MessageActionSecureValuesSentMe,
    0xaa786345: types.MessageActionSetChatTheme,
    0x5060a3f4: types.MessageActionSetChatWallPaper,
    0x3c134d7b: types.MessageActionSetMessagesTTL,
    0x4717e8a4: types.MessageActionStarGift,
    0xacdfcb81: types.MessageActionStarGiftUnique,
    0x57de635e: types.MessageActionSuggestProfilePhoto,
    0x0d999256: types.MessageActionTopicCreate,
    0xc0944820: types.MessageActionTopicEdit,
    0xb4c38cb5: types.MessageActionWebViewDataSent,
    0x47dd8079: types.MessageActionWebViewDataSentMe,
    0x26b5dde6: types.messages.MessageEditData,
    0x90a6ca84: types.MessageEmpty,
    0x761e6af4: types.MessageEntityBankCard,
    0xf1ccaaac: types.MessageEntityBlockquote,
    0xbd610bc9: types.MessageEntityBold,
    0x6cef8ac7: types.MessageEntityBotCommand,
    0x4c4e743f: types.MessageEntityCashtag,
    0x28a20571: types.MessageEntityCode,
    0xc8cf05f8: types.MessageEntityCustomEmoji,
    0x64e475c2: types.MessageEntityEmail,
    0x6f635b0d: types.MessageEntityHashtag,
    0x826f8b60: types.MessageEntityItalic,
    0xfa04579d: types.MessageEntityMention,
    0xdc7b1140: types.MessageEntityMentionName,
    0x9b69e34b: types.MessageEntityPhone,
    0x73924be0: types.MessageEntityPre,
    0x32ca960f: types.MessageEntitySpoiler,
    0xbf0693d4: types.MessageEntityStrike,
    0x76a6d327: types.MessageEntityTextUrl,
    0x9c4e7e8b: types.MessageEntityUnderline,
    0xbb92ba95: types.MessageEntityUnknown,
    0x6ed02538: types.MessageEntityUrl,
    0xee479c64: types.MessageExtendedMedia,
    0xad628cc8: types.MessageExtendedMediaPreview,
    0x4e4df4bb: types.MessageFwdHeader,
    0x70322949: types.MessageMediaContact,
    0x3f7ee58b: types.MessageMediaDice,
    0x52d8ccd9: types.MessageMediaDocument,
    0x3ded6320: types.MessageMediaEmpty,
    0xfdb19008: types.MessageMediaGame,
    0x56e0d474: types.MessageMediaGeo,
    0xb940c666: types.MessageMediaGeoLive,
    0xaa073beb: types.MessageMediaGiveaway,
    0xceaa3ea1: types.MessageMediaGiveawayResults,
    0xf6a548d3: types.MessageMediaInvoice,
    0xa8852491: types.MessageMediaPaidMedia,
    0x695150d7: types.MessageMediaPhoto,
    0x4bd6e798: types.MessageMediaPoll,
    0x68cb6283: types.MessageMediaStory,
    0x9f84f49e: types.MessageMediaUnsupported,
    0x2ec0533f: types.MessageMediaVenue,
    0xddf10c3b: types.MessageMediaWebPage,
    0x8c79b63c: types.MessagePeerReaction,
    0xb6cc2d5c: types.MessagePeerVote,
    0x74cda504: types.MessagePeerVoteInputOption,
    0x4628f6e6: types.MessagePeerVoteMultiple,
    0x0ae30253: types.MessageRange,
    0x0a339f0b: types.MessageReactions,
    0x31bd492d: types.messages.MessageReactionsList,
    0x4ba3a95a: types.MessageReactor,
    0x83d60fc2: types.MessageReplies,
    0xafbc09db: types.MessageReplyHeader,
    0x0e5af939: types.MessageReplyStoryHeader,
    0x7903e3d9: types.MessageReportOption,
    0xd3d28540: types.MessageService,
    0x7fe91c14: types.stats.MessageStats,
    0x455b853d: types.MessageViews,
    0xb6c4f543: types.messages.MessageViews,
    0x8c718e87: types.messages.Messages,
    0x74535f21: types.messages.MessagesNotModified,
    0x3a54685e: types.messages.MessagesSlice,
    0xa2875319: functions.messages.MigrateChatRequest,
    0x628c9224: types.MissingInvitee,
    0x276d3ec6: types.MsgDetailedInfo,
    0x809db6df: types.MsgNewDetailedInfo,
    0x7d861a08: types.MsgResendReq,
    0x62d6b459: types.MsgsAck,
    0x8cc0d131: types.MsgsAllInfo,
    0x04deb57d: types.MsgsStateInfo,
    0xda69fb52: types.MsgsStateReq,
    0xc448415c: types.MyBoost,
    0x9ae228e2: types.premium.MyBoosts,
    0xfaff629d: types.messages.MyStickers,
    0x8e1a1775: types.NearestDc,
    0x9ec20908: types.NewSessionCreated,
    0xc45a6536: types.help.NoAppUpdate,
    0x97e8bebe: types.NotificationSoundDefault,
    0x830b9ae4: types.NotificationSoundLocal,
    0x6f0c34df: types.NotificationSoundNone,
    0xff6c8049: types.NotificationSoundRingtone,
    0xd612e8ef: types.NotifyBroadcasts,
    0xc007cec3: types.NotifyChats,
    0x226e6308: types.NotifyForumTopic,
    0x9fd40bd8: types.NotifyPeer,
    0xb4c83b4c: types.NotifyUsers,
    0x3bb842ac: types.OutboxReadDate,
    0x83c95aec: types.PQInnerData,
    0xa9f55f95: types.PQInnerDataDc,
    0x3c6a84d4: types.PQInnerDataTemp,
    0x56fddf88: types.PQInnerDataTempDc,
    0x98657f0d: types.Page,
    0xce0d37b0: types.PageBlockAnchor,
    0x804361ea: types.PageBlockAudio,
    0xbaafe5e0: types.PageBlockAuthorDate,
    0x263d7c26: types.PageBlockBlockquote,
    0xef1751b5: types.PageBlockChannel,
    0x65a0fa4d: types.PageBlockCollage,
    0x39f23300: types.PageBlockCover,
    0x76768bed: types.PageBlockDetails,
    0xdb20b188: types.PageBlockDivider,
    0xa8718dc5: types.PageBlockEmbed,
    0xf259a80b: types.PageBlockEmbedPost,
    0x48870999: types.PageBlockFooter,
    0xbfd064ec: types.PageBlockHeader,
    0x1e148390: types.PageBlockKicker,
    0xe4e88011: types.PageBlockList,
    0xa44f3ef6: types.PageBlockMap,
    0x9a8ae1e1: types.PageBlockOrderedList,
    0x467a0766: types.PageBlockParagraph,
    0x1759c560: types.PageBlockPhoto,
    0xc070d93e: types.PageBlockPreformatted,
    0x4f4456d3: types.PageBlockPullquote,
    0x16115a96: types.PageBlockRelatedArticles,
    0x031f9590: types.PageBlockSlideshow,
    0xf12bb6e1: types.PageBlockSubheader,
    0x8ffa9a1f: types.PageBlockSubtitle,
    0xbf4dea82: types.PageBlockTable,
    0x70abc3fd: types.PageBlockTitle,
    0x13567e8a: types.PageBlockUnsupported,
    0x7c8fe7b6: types.PageBlockVideo,
    0x6f747657: types.PageCaption,
    0x25e073fc: types.PageListItemBlocks,
    0xb92fb6cd: types.PageListItemText,
    0x98dd8936: types.PageListOrderedItemBlocks,
    0x5e068047: types.PageListOrderedItemText,
    0xb390dc08: types.PageRelatedArticle,
    0x34566b6a: types.PageTableCell,
    0xe0c0c5e5: types.PageTableRow,
    0x1e109708: types.account.PaidMessagesRevenue,
    0x1f0c1ad9: types.PaidReactionPrivacyAnonymous,
    0x206ad49e: types.PaidReactionPrivacyDefault,
    0xdc6cfcf0: types.PaidReactionPrivacyPeer,
    0xa098d6af: types.help.PassportConfig,
    0xbfb9f457: types.help.PassportConfigNotModified,
    0x957b50fb: types.account.Password,
    0xc23727c9: types.account.PasswordInputSettings,
    0x3a912d4a: types.PasswordKdfAlgoSHA256SHA256PBKDF2HMACSHA512iter100000SHA256ModPow,
    0xd45ab096: types.PasswordKdfAlgoUnknown,
    0x137948a5: types.auth.PasswordRecovery,
    0x9a5c33e5: types.account.PasswordSettings,
    0xea02c27e: types.PaymentCharge,
    0xa0058751: types.payments.PaymentForm,
    0x88f8f21b: types.PaymentFormMethod,
    0xb425cfe1: types.payments.PaymentFormStarGift,
    0x7bf6b15c: types.payments.PaymentFormStars,
    0x70c4fe03: types.payments.PaymentReceipt,
    0xdabbf83a: types.payments.PaymentReceiptStars,
    0x909c3f94: types.PaymentRequestedInfo,
    0x4e5f810d: types.payments.PaymentResult,
    0xcdc27a1f: types.PaymentSavedCredentialsCard,
    0xd8411139: types.payments.PaymentVerificationNeeded,
    0xe8fd8014: types.PeerBlocked,
    0xa2a5371e: types.PeerChannel,
    0x36c6019a: types.PeerChat,
    0xb54b5acf: types.PeerColor,
    0xadec6ebe: types.help.PeerColorOption,
    0x767d61eb: types.help.PeerColorProfileSet,
    0x26219a58: types.help.PeerColorSet,
    0x00f8ed08: types.help.PeerColors,
    0x2ba1f5ce: types.help.PeerColorsNotModified,
    0x3371c354: types.messages.PeerDialogs,
    0xca461b5d: types.PeerLocated,
    0x99622c0c: types.PeerNotifySettings,
    0xf8ec284b: types.PeerSelfLocated,
    0xf47741f7: types.PeerSettings,
    0x6880b94d: types.messages.PeerSettings,
    0x9a35e999: types.PeerStories,
    0xcae68768: types.stories.PeerStories,
    0x59511722: types.PeerUser,
    0x3ba5940c: types.PhoneCall,
    0xec82e140: types.phone.PhoneCall,
    0x22fd7181: types.PhoneCallAccepted,
    0xafe2b839: types.PhoneCallDiscardReasonAllowGroupCall,
    0xfaf7e8c9: types.PhoneCallDiscardReasonBusy,
    0xe095c1a0: types.PhoneCallDiscardReasonDisconnect,
    0x57adc690: types.PhoneCallDiscardReasonHangup,
    0x85e42301: types.PhoneCallDiscardReasonMissed,
    0xf9d25503: types.PhoneCallDiscarded,
    0x5366c915: types.PhoneCallEmpty,
    0xfc878fc8: types.PhoneCallProtocol,
    0x45361c63: types.PhoneCallRequested,
    0xeed42858: types.PhoneCallWaiting,
    0x9cc123c7: types.PhoneConnection,
    0x635fe375: types.PhoneConnectionWebrtc,
    0xfb197a65: types.Photo,
    0x20212ca8: types.photos.Photo,
    0x021e1ad6: types.PhotoCachedSize,
    0x2331b22d: types.PhotoEmpty,
    0xd8214d41: types.PhotoPathSize,
    0x75c78e60: types.PhotoSize,
    0x0e17e23c: types.PhotoSizeEmpty,
    0xfa3efb95: types.PhotoSizeProgressive,
    0xe0b0bc2e: types.PhotoStrippedSize,
    0x8dca6aa5: types.photos.Photos,
    0x15051f54: types.photos.PhotosSlice,
    0x7abe77ec: functions.PingRequest,
    0xf3427b8c: functions.PingDelayDisconnectRequest,
    0x58747131: types.Poll,
    0xff16e2ca: types.PollAnswer,
    0x3b6ddad2: types.PollAnswerVoters,
    0x7adf2420: types.PollResults,
    0x347773c5: types.Pong,
    0x1991b13b: types.bots.PopularAppBots,
    0x5ce14175: types.PopularContact,
    0x1e8caaeb: types.PostAddress,
    0xe7058e7f: types.PostInteractionCountersMessage,
    0x8a480e27: types.PostInteractionCountersStory,
    0x257e962b: types.PremiumGiftCodeOption,
    0x5334759c: types.help.PremiumPromo,
    0x5f2d1df2: types.PremiumSubscriptionOption,
    0xb2539d54: types.PrepaidGiveaway,
    0x9a9d77e0: types.PrepaidStarsGiveaway,
    0xff57708d: types.messages.PreparedInlineMessage,
    0x0ca71d64: types.bots.PreviewInfo,
    0xa486b761: types.PrivacyKeyAbout,
    0x42ffd42b: types.PrivacyKeyAddedByPhone,
    0x2000a518: types.PrivacyKeyBirthday,
    0x500e6dfa: types.PrivacyKeyChatInvite,
    0x69ec56a3: types.PrivacyKeyForwards,
    0x17d348d2: types.PrivacyKeyNoPaidMessages,
    0x3d662b7b: types.PrivacyKeyPhoneCall,
    0xd19ae46d: types.PrivacyKeyPhoneNumber,
    0x39491cc8: types.PrivacyKeyPhoneP2P,
    0x96151fed: types.PrivacyKeyProfilePhoto,
    0x2ca4fdf8: types.PrivacyKeyStarGiftsAutoSave,
    0xbc2eab30: types.PrivacyKeyStatusTimestamp,
    0x0697f414: types.PrivacyKeyVoiceMessages,
    0x50a04e45: types.account.PrivacyRules,
    0x65427b82: types.PrivacyValueAllowAll,
    0x21461b5d: types.PrivacyValueAllowBots,
    0x6b134e8e: types.PrivacyValueAllowChatParticipants,
    0xf7e8d89b: types.PrivacyValueAllowCloseFriends,
    0xfffe1bac: types.PrivacyValueAllowContacts,
    0xece9814b: types.PrivacyValueAllowPremium,
    0xb8905fb2: types.PrivacyValueAllowUsers,
    0x8b73e763: types.PrivacyValueDisallowAll,
    0xf6a5f82f: types.PrivacyValueDisallowBots,
    0x41c87565: types.PrivacyValueDisallowChatParticipants,
    0xf888fa1a: types.PrivacyValueDisallowContacts,
    0xe4621141: types.PrivacyValueDisallowUsers,
    0xb0d81a83: functions.messages.ProlongWebViewRequest,
    0x8c39793f: types.help.PromoData,
    0x98f6ac75: types.help.PromoDataEmpty,
    0x01f2bf4a: types.PublicForwardMessage,
    0xedf3add0: types.PublicForwardStory,
    0x93037e20: types.stats.PublicForwards,
    0xc68d6695: types.messages.QuickReplies,
    0x5f91eb5b: types.messages.QuickRepliesNotModified,
    0x0697102b: types.QuickReply,
    0x7f1d072f: functions.messages.RateTranscribedAudioRequest,
    0xa3d1cb80: types.ReactionCount,
    0x8935fc73: types.ReactionCustomEmoji,
    0x1b2286b8: types.ReactionEmoji,
    0x79f5d419: types.ReactionEmpty,
    0x4b9e22a0: types.ReactionNotificationsFromAll,
    0xbac3a61a: types.ReactionNotificationsFromContacts,
    0x523da4eb: types.ReactionPaid,
    0xeafdf716: types.messages.Reactions,
    0xb06fdbdf: types.messages.ReactionsNotModified,
    0x56e34970: types.ReactionsNotifySettings,
    0xf731a9f4: functions.messages.ReadDiscussionRequest,
    0x7f4b690a: functions.messages.ReadEncryptedHistoryRequest,
    0x5b118126: functions.messages.ReadFeaturedStickersRequest,
    0x0e306d3a: functions.messages.ReadHistoryRequest,
    0xcc104937: functions.channels.ReadHistoryRequest,
    0x36e5bf4d: functions.messages.ReadMentionsRequest,
    0x36a73f77: functions.messages.ReadMessageContentsRequest,
    0xeab5dc38: functions.channels.ReadMessageContentsRequest,
    0x4a4ff172: types.ReadParticipantDate,
    0x54aa7f8e: functions.messages.ReadReactionsRequest,
    0xa556dac8: functions.stories.ReadStoriesRequest,
    0x17d54f61: functions.phone.ReceivedCallRequest,
    0x05a954c0: functions.messages.ReceivedMessagesRequest,
    0xa384b779: types.ReceivedNotifyMessage,
    0x55a5bb66: functions.messages.ReceivedQueueRequest,
    0xb2da71d2: types.RecentMeUrlChat,
    0xeb49081d: types.RecentMeUrlChatInvite,
    0xbc0a57dc: types.RecentMeUrlStickerSet,
    0x46e1d13d: types.RecentMeUrlUnknown,
    0xb92c09e2: types.RecentMeUrlUser,
    0x0e0310d7: types.help.RecentMeUrls,
    0x88d37c56: types.messages.RecentStickers,
    0x0b17f890: types.messages.RecentStickersNotModified,
    0x37096c70: functions.auth.RecoverPasswordRequest,
    0x25ae8f4a: functions.payments.RefundStarsChargeRequest,
    0xec86017a: functions.account.RegisterDeviceRequest,
    0xf7760f51: functions.stickers.RemoveStickerFromSetRequest,
    0x124b1c00: functions.stickers.RenameStickerSetRequest,
    0x3b1adf37: functions.messages.ReorderPinnedDialogsRequest,
    0x2950a18f: functions.channels.ReorderPinnedForumTopicsRequest,
    0x8b716587: functions.messages.ReorderPinnedSavedDialogsRequest,
    0xb627f3aa: functions.bots.ReorderPreviewMediasRequest,
    0x60331907: functions.messages.ReorderQuickRepliesRequest,
    0x78337739: functions.messages.ReorderStickerSetsRequest,
    0xef500eab: functions.account.ReorderUsernamesRequest,
    0xb45ced1d: functions.channels.ReorderUsernamesRequest,
    0x9709b1c2: functions.bots.ReorderUsernamesRequest,
    0x4696459a: functions.stickers.ReplaceStickerRequest,
    0x48a30254: types.ReplyInlineMarkup,
    0x86b40b08: types.ReplyKeyboardForceReply,
    0xa03e5b85: types.ReplyKeyboardHide,
    0x85dd99d1: types.ReplyKeyboardMarkup,
    0xfc78af9b: functions.messages.ReportRequest,
    0x19d8eb45: functions.stories.ReportRequest,
    0xa850a693: functions.channels.ReportAntiSpamFalsePositiveRequest,
    0x4b0c8c0f: functions.messages.ReportEncryptedSpamRequest,
    0x5a6d7395: functions.messages.ReportMessagesDeliveryRequest,
    0xcb9deff6: functions.auth.ReportMissingCodeRequest,
    0xc5ba3d86: functions.account.ReportPeerRequest,
    0xfa8cc6f5: functions.account.ReportProfilePhotoRequest,
    0x3f64c076: functions.messages.ReportReactionRequest,
    0x6f09ac31: types.ReportResultAddComment,
    0xf0e4e0b6: types.ReportResultChooseOption,
    0x8db33c4b: types.ReportResultReported,
    0xcf1592db: functions.messages.ReportSpamRequest,
    0xf44a8315: functions.channels.ReportSpamRequest,
    0x12cbf0c4: functions.messages.ReportSponsoredMessageRequest,
    0xd712e4be: functions.ReqDHParamsRequest,
    0x60469778: functions.ReqPqRequest,
    0xbe7e8ef1: functions.ReqPqMultiRequest,
    0x53618bce: functions.messages.RequestAppWebViewRequest,
    0xa6c4600c: functions.phone.RequestCallRequest,
    0xf64daf43: functions.messages.RequestEncryptionRequest,
    0x8e39261e: functions.auth.RequestFirebaseSmsRequest,
    0xc9e01e7b: functions.messages.RequestMainWebViewRequest,
    0xd897bc66: functions.auth.RequestPasswordRecoveryRequest,
    0x339bef6c: types.RequestPeerTypeBroadcast,
    0xc9f06e1b: types.RequestPeerTypeChat,
    0x5f3b8a00: types.RequestPeerTypeUser,
    0x413a3e73: functions.messages.RequestSimpleWebViewRequest,
    0x198fb446: functions.messages.RequestUrlAuthRequest,
    0x269dc2c1: functions.messages.RequestWebViewRequest,
    0x8ba403e4: types.RequestedPeerChannel,
    0x7307544f: types.RequestedPeerChat,
    0xd62ff46a: types.RequestedPeerUser,
    0x050a9839: types.RequirementToContactEmpty,
    0xb4f67e93: types.RequirementToContactPaidMessages,
    0xe581e4e9: types.RequirementToContactPremium,
    0x05162463: types.ResPQ,
    0xcae47523: functions.auth.ResendCodeRequest,
    0x7a7f2a15: functions.account.ResendPasswordEmailRequest,
    0xdf77f3bc: functions.account.ResetAuthorizationRequest,
    0x9fab0d1a: functions.auth.ResetAuthorizationsRequest,
    0x3d8de0f9: functions.bots.ResetBotCommandsRequest,
    0x7e960193: functions.auth.ResetLoginEmailRequest,
    0xdb7e1747: functions.account.ResetNotifySettingsRequest,
    0x9308ce1b: functions.account.ResetPasswordRequest,
    0xe3779861: types.account.ResetPasswordFailedWait,
    0xe926d63e: types.account.ResetPasswordOk,
    0xe9effc7d: types.account.ResetPasswordRequestedWait,
    0x879537f1: functions.contacts.ResetSavedRequest,
    0x1ae373ac: functions.contacts.ResetTopPeerRatingRequest,
    0xbb3b9804: functions.account.ResetWallPapersRequest,
    0x2d01b9ef: functions.account.ResetWebAuthorizationRequest,
    0x682d2594: functions.account.ResetWebAuthorizationsRequest,
    0x5492e5ee: functions.account.ResolveBusinessChatLinkRequest,
    0x8af94344: functions.contacts.ResolvePhoneRequest,
    0x725afbbc: functions.contacts.ResolveUsernameRequest,
    0x9a23af21: types.account.ResolvedBusinessChatLinks,
    0x7f077ad9: types.contacts.ResolvedPeer,
    0x9ae91519: functions.channels.RestrictSponsoredMessagesRequest,
    0xd072acb4: types.RestrictionReason,
    0x9b2754a8: functions.upload.ReuploadCdnFileRequest,
    0xa43ad8b7: types.RpcAnswerDropped,
    0xcd78e586: types.RpcAnswerDroppedRunning,
    0x5e2ad36e: types.RpcAnswerUnknown,
    0x58e4a740: functions.RpcDropAnswerRequest,
    0x2144ca19: types.RpcError,
    0x6f02f748: functions.help.SaveAppLogRequest,
    0x76f36233: functions.account.SaveAutoDownloadSettingsRequest,
    0xd69b8361: functions.account.SaveAutoSaveSettingsRequest,
    0xde7b673d: functions.upload.SaveBigFilePartRequest,
    0x277add7e: functions.phone.SaveCallDebugRequest,
    0x41248786: functions.phone.SaveCallLogRequest,
    0x575e1f8c: functions.phone.SaveDefaultGroupCallJoinAsRequest,
    0xccfddf96: functions.messages.SaveDefaultSendAsRequest,
    0xd372c5ce: functions.messages.SaveDraftRequest,
    0xb304a621: functions.upload.SaveFilePartRequest,
    0x327a30cb: functions.messages.SaveGifRequest,
    0xf21f7f2f: functions.messages.SavePreparedInlineMessageRequest,
    0x392718f8: functions.messages.SaveRecentStickerRequest,
    0x3dea5b03: functions.account.SaveRingtoneRequest,
    0x899fe31d: functions.account.SaveSecureValueRequest,
    0x2a2a697c: functions.payments.SaveStarGiftRequest,
    0xf257106c: functions.account.SaveThemeRequest,
    0x6c5a5b37: functions.account.SaveWallPaperRequest,
    0xbd87cb6c: types.SavedDialog,
    0xf83ae221: types.messages.SavedDialogs,
    0xc01f6fe8: types.messages.SavedDialogsNotModified,
    0x44ba9dd9: types.messages.SavedDialogsSlice,
    0x84a02a0d: types.messages.SavedGifs,
    0xe8025ca2: types.messages.SavedGifsNotModified,
    0xfb8fe43c: types.payments.SavedInfo,
    0x1142bd56: types.SavedPhoneContact,
    0xcb6ff828: types.SavedReactionTag,
    0x3259950a: types.messages.SavedReactionTags,
    0x889b59ef: types.messages.SavedReactionTagsNotModified,
    0xb7263f6d: types.account.SavedRingtone,
    0x1f307eb7: types.account.SavedRingtoneConverted,
    0xc1e92cc5: types.account.SavedRingtones,
    0xfbf6e8b1: types.account.SavedRingtonesNotModified,
    0x6056dba5: types.SavedStarGift,
    0x95f389b1: types.payments.SavedStarGifts,
    0x11f812d8: functions.contacts.SearchRequest,
    0x29ee847a: functions.messages.SearchRequest,
    0xe844ebff: types.messages.SearchCounter,
    0x2c11c0d7: functions.messages.SearchCustomEmojiRequest,
    0x92b4494c: functions.messages.SearchEmojiStickerSetsRequest,
    0x4bc6589a: functions.messages.SearchGlobalRequest,
    0xd19f987b: functions.channels.SearchPostsRequest,
    0xd1810907: functions.stories.SearchPostsRequest,
    0x7f648b67: types.SearchResultPosition,
    0x147ee23c: types.messages.SearchResultsCalendar,
    0xc9b0539f: types.SearchResultsCalendarPeriod,
    0x53b22baf: types.messages.SearchResultsPositions,
    0x107e31a0: functions.messages.SearchSentMediaRequest,
    0x35705b8a: functions.messages.SearchStickerSetsRequest,
    0x29b1c66a: functions.messages.SearchStickersRequest,
    0x33f0ea47: types.SecureCredentialsEncrypted,
    0x8aeabec3: types.SecureData,
    0x7d09c27e: types.SecureFile,
    0x64199744: types.SecureFileEmpty,
    0xbbf2dda0: types.SecurePasswordKdfAlgoPBKDF2HMACSHA512iter100000,
    0x86471d92: types.SecurePasswordKdfAlgoSHA512,
    0x004a8537: types.SecurePasswordKdfAlgoUnknown,
    0x21ec5a5f: types.SecurePlainEmail,
    0x7d6099dd: types.SecurePlainPhone,
    0x829d99da: types.SecureRequiredType,
    0x027477b4: types.SecureRequiredTypeOneOf,
    0x1527bcac: types.SecureSecretSettings,
    0x187fa0ca: types.SecureValue,
    0x869d758f: types.SecureValueError,
    0xe8a40bd9: types.SecureValueErrorData,
    0x7a700873: types.SecureValueErrorFile,
    0x666220e9: types.SecureValueErrorFiles,
    0x00be3dfa: types.SecureValueErrorFrontSide,
    0x868a2aa5: types.SecureValueErrorReverseSide,
    0xe537ced6: types.SecureValueErrorSelfie,
    0xa1144770: types.SecureValueErrorTranslationFile,
    0x34636dd8: types.SecureValueErrorTranslationFiles,
    0xed1ecdb0: types.SecureValueHash,
    0xcbe31e26: types.SecureValueTypeAddress,
    0x89137c0d: types.SecureValueTypeBankStatement,
    0x06e425c4: types.SecureValueTypeDriverLicense,
    0x8e3ca7ee: types.SecureValueTypeEmail,
    0xa0d0744b: types.SecureValueTypeIdentityCard,
    0x99a48f23: types.SecureValueTypeInternalPassport,
    0x3dac6a00: types.SecureValueTypePassport,
    0x99e3806a: types.SecureValueTypePassportRegistration,
    0x9d2a81e3: types.SecureValueTypePersonalDetails,
    0xb320aadb: types.SecureValueTypePhone,
    0x8b883488: types.SecureValueTypeRentalAgreement,
    0xea02ec33: types.SecureValueTypeTemporaryRegistration,
    0xfc36954e: types.SecureValueTypeUtilityBill,
    0xb81c7034: types.SendAsPeer,
    0xf496b0c6: types.channels.SendAsPeers,
    0x91b2d060: functions.messages.SendBotRequestedPeerRequest,
    0x82574ae5: functions.account.SendChangePhoneCodeRequest,
    0xa677244f: functions.auth.SendCodeRequest,
    0x1b3faa88: functions.account.SendConfirmPhoneCodeRequest,
    0xaa2769ed: functions.bots.SendCustomRequestRequest,
    0x44fa7a15: functions.messages.SendEncryptedRequest,
    0x5559481d: functions.messages.SendEncryptedFileRequest,
    0x32d439a4: functions.messages.SendEncryptedServiceRequest,
    0xc0cf7646: functions.messages.SendInlineBotResultRequest,
    0xa550cd78: functions.messages.SendMediaRequest,
    0xfbf2340a: functions.messages.SendMessageRequest,
    0xfd5ec8f5: types.SendMessageCancelAction,
    0x628cbc6f: types.SendMessageChooseContactAction,
    0xb05ac6b1: types.SendMessageChooseStickerAction,
    0x25972bcb: types.SendMessageEmojiInteraction,
    0xb665902e: types.SendMessageEmojiInteractionSeen,
    0xdd6a8f48: types.SendMessageGamePlayAction,
    0x176f8ba1: types.SendMessageGeoLocationAction,
    0xdbda9246: types.SendMessageHistoryImportAction,
    0xd52f73f7: types.SendMessageRecordAudioAction,
    0x88f27fbc: types.SendMessageRecordRoundAction,
    0xa187d66f: types.SendMessageRecordVideoAction,
    0x16bf744e: types.SendMessageTypingAction,
    0xf351d7ab: types.SendMessageUploadAudioAction,
    0xaa0cd9e4: types.SendMessageUploadDocumentAction,
    0xd1d34a26: types.SendMessageUploadPhotoAction,
    0x243e1c66: types.SendMessageUploadRoundAction,
    0xe9763aec: types.SendMessageUploadVideoAction,
    0x1bf89d74: functions.messages.SendMultiMediaRequest,
    0x58bbcb50: functions.messages.SendPaidReactionRequest,
    0x2d03522f: functions.payments.SendPaymentFormRequest,
    0x6c750de1: functions.messages.SendQuickReplyMessagesRequest,
    0xd30d78d4: functions.messages.SendReactionRequest,
    0x7fd736b2: functions.stories.SendReactionRequest,
    0xbd38850a: functions.messages.SendScheduledMessagesRequest,
    0xa1405817: functions.messages.SendScreenshotNotificationRequest,
    0xff7a9383: functions.phone.SendSignalingDataRequest,
    0x7998c914: functions.payments.SendStarsFormRequest,
    0xe4e6694b: functions.stories.SendStoryRequest,
    0x98e037bb: functions.account.SendVerifyEmailCodeRequest,
    0xa5a356f9: functions.account.SendVerifyPhoneCodeRequest,
    0x10ea6184: functions.messages.SendVoteRequest,
    0xdc0242c8: functions.messages.SendWebViewDataRequest,
    0x0a4314f5: functions.messages.SendWebViewResultMessageRequest,
    0x5e002502: types.auth.SentCode,
    0xd7cef980: types.auth.SentCodePaymentRequired,
    0x2390fe44: types.auth.SentCodeSuccess,
    0x3dbb5986: types.auth.SentCodeTypeApp,
    0x5353e5a7: types.auth.SentCodeTypeCall,
    0xf450f59b: types.auth.SentCodeTypeEmailCode,
    0x009fd736: types.auth.SentCodeTypeFirebaseSms,
    0xab03c6d9: types.auth.SentCodeTypeFlashCall,
    0xd9565c39: types.auth.SentCodeTypeFragmentSms,
    0x82006484: types.auth.SentCodeTypeMissedCall,
    0xa5491dea: types.auth.SentCodeTypeSetUpEmailRequired,
    0xc000bba2: types.auth.SentCodeTypeSms,
    0xb37794af: types.auth.SentCodeTypeSmsPhrase,
    0xa416ac81: types.auth.SentCodeTypeSmsWord,
    0x811f854f: types.account.SentEmailCode,
    0x9493ff32: types.messages.SentEncryptedFile,
    0x560f8935: types.messages.SentEncryptedMessage,
    0xb5890dba: types.ServerDHInnerData,
    0x79cb045d: types.ServerDHParamsFail,
    0xd0e8075c: types.ServerDHParamsOk,
    0x2442485e: functions.account.SetAccountTTLRequest,
    0xbf899aa0: functions.account.SetAuthorizationTTLRequest,
    0x94c65c76: functions.contacts.SetBlockedRequest,
    0xad399cee: functions.channels.SetBoostsToUnblockRestrictionsRequest,
    0x788464e1: functions.bots.SetBotBroadcastDefaultAdminRightsRequest,
    0xd58f130a: functions.messages.SetBotCallbackAnswerRequest,
    0x0517165a: functions.bots.SetBotCommandsRequest,
    0x925ec9ea: functions.bots.SetBotGroupDefaultAdminRightsRequest,
    0x10cf3123: functions.bots.SetBotInfoRequest,
    0x4504d54f: functions.bots.SetBotMenuButtonRequest,
    0x09c2dd95: functions.messages.SetBotPrecheckoutResultsRequest,
    0xe5f672fa: functions.messages.SetBotShippingResultsRequest,
    0xec22cfcd: functions.help.SetBotUpdatesStatusRequest,
    0x59ead627: functions.phone.SetCallRatingRequest,
    0x864b2581: functions.messages.SetChatAvailableReactionsRequest,
    0xe63be13f: functions.messages.SetChatThemeRequest,
    0x8ffacae1: functions.messages.SetChatWallPaperRequest,
    0xcff43f61: functions.account.SetContactSignUpNotificationRequest,
    0xb574b16b: functions.account.SetContentSettingsRequest,
    0x8b89dfbd: functions.bots.SetCustomVerificationRequest,
    0x9eb51445: functions.messages.SetDefaultHistoryTTLRequest,
    0x4f47a016: functions.messages.SetDefaultReactionRequest,
    0x40582bb2: functions.channels.SetDiscussionGroupRequest,
    0x3cd930b7: functions.channels.SetEmojiStickersRequest,
    0x791451ed: functions.messages.SetEncryptedTypingRequest,
    0x8ef8ecc0: functions.messages.SetGameScoreRequest,
    0x1edaaac2: functions.account.SetGlobalPrivacySettingsRequest,
    0xb80e5fe4: functions.messages.SetHistoryTTLRequest,
    0xbb12a419: functions.messages.SetInlineBotResultsRequest,
    0x15ad9f64: functions.messages.SetInlineGameScoreRequest,
    0xc9f81ce8: functions.account.SetPrivacyRequest,
    0x316ce548: functions.account.SetReactionsNotifySettingsRequest,
    0x90c894b5: functions.users.SetSecureValueErrorsRequest,
    0xa76a5392: functions.stickers.SetStickerSetThumbRequest,
    0xea8ca4f9: functions.channels.SetStickersRequest,
    0x58943ee2: functions.messages.SetTypingRequest,
    0xf5045f1f: functions.SetClientDHParamsRequest,
    0xb6213cdf: types.ShippingOption,
    0x8d52a951: functions.auth.SignInRequest,
    0xaac7b717: functions.auth.SignUpRequest,
    0xe6a1eeb8: types.SmsJob,
    0xd92c2285: types.SpeakingInGroupCallAction,
    0x4d93a990: types.SponsoredMessage,
    0x430d3150: types.SponsoredMessageReportOption,
    0x3e3bcf2f: types.channels.SponsoredMessageReportResultAdsHidden,
    0x846f9e42: types.channels.SponsoredMessageReportResultChooseOption,
    0xad798849: types.channels.SponsoredMessageReportResultReported,
    0xc9ee1d87: types.messages.SponsoredMessages,
    0x1839490f: types.messages.SponsoredMessagesEmpty,
    0xc69708d3: types.SponsoredPeer,
    0xeb032884: types.contacts.SponsoredPeers,
    0xea32b4b1: types.contacts.SponsoredPeersEmpty,
    0x02cc73c8: types.StarGift,
    0x94271762: types.StarGiftAttributeBackdrop,
    0x39d99013: types.StarGiftAttributeModel,
    0xe0bff26c: types.StarGiftAttributeOriginalDetails,
    0x13acff19: types.StarGiftAttributePattern,
    0x5c62d151: types.StarGiftUnique,
    0x167bd90b: types.payments.StarGiftUpgradePreview,
    0x84aa3a9c: types.payments.StarGiftWithdrawalUrl,
    0x901689ea: types.payments.StarGifts,
    0xa388a368: types.payments.StarGiftsNotModified,
    0xdd0c66f2: types.StarRefProgram,
    0xbbb6b4a3: types.StarsAmount,
    0x5e0589f1: types.StarsGiftOption,
    0x94ce852a: types.StarsGiveawayOption,
    0x54236209: types.StarsGiveawayWinnersOption,
    0x394e7f21: types.payments.StarsRevenueAdsAccountUrl,
    0xc92bb73b: types.payments.StarsRevenueStats,
    0xfebe5491: types.StarsRevenueStatus,
    0x1dab80b7: types.payments.StarsRevenueWithdrawalUrl,
    0x6c9ce8ed: types.payments.StarsStatus,
    0x2e6eab1a: types.StarsSubscription,
    0x05416d58: types.StarsSubscriptionPricing,
    0x0bd915c0: types.StarsTopupOption,
    0xa39fd94a: types.StarsTransaction,
    0xd80da15d: types.StarsTransactionPeer,
    0xf9677aad: types.StarsTransactionPeerAPI,
    0x60682812: types.StarsTransactionPeerAds,
    0xb457b375: types.StarsTransactionPeerAppStore,
    0xe92fd902: types.StarsTransactionPeerFragment,
    0x7b560a0b: types.StarsTransactionPeerPlayMarket,
    0x250dbaf8: types.StarsTransactionPeerPremiumBot,
    0x95f2bfe4: types.StarsTransactionPeerUnsupported,
    0xe6df7378: functions.messages.StartBotRequest,
    0xb43df344: functions.messages.StartHistoryImportRequest,
    0x5680e342: functions.phone.StartScheduledGroupCallRequest,
    0xa56c2a3e: types.updates.State,
    0xcb43acde: types.StatsAbsValueAndPrev,
    0xb637edaf: types.StatsDateRangeDays,
    0x8ea464b6: types.StatsGraph,
    0x4a27eb2d: types.StatsGraphAsync,
    0xbedc9822: types.StatsGraphError,
    0xd7584c87: types.StatsGroupTopAdmin,
    0x535f779d: types.StatsGroupTopInviter,
    0x9d04af9b: types.StatsGroupTopPoster,
    0xcbce2fe0: types.StatsPercentValue,
    0x47a971e0: types.StatsURL,
    0x2aee9191: types.smsjobs.Status,
    0xfcfeb29c: types.StickerKeyword,
    0x12b299d4: types.StickerPack,
    0x2dd14edc: types.StickerSet,
    0x6e153f16: types.messages.StickerSet,
    0x6410a5d2: types.StickerSetCovered,
    0x40d13c0e: types.StickerSetFullCovered,
    0x35e410a8: types.messages.StickerSetInstallResultArchive,
    0x38641628: types.messages.StickerSetInstallResultSuccess,
    0x3407e51b: types.StickerSetMultiCovered,
    0x77b15d1c: types.StickerSetNoCovered,
    0xd3f924eb: types.messages.StickerSetNotModified,
    0x30a6ec7e: types.messages.Stickers,
    0xf1749a22: types.messages.StickersNotModified,
    0x63c3dd0a: types.stories.Stories,
    0x712e27fd: types.StoriesStealthMode,
    0xb826e150: types.StoryFwdHeader,
    0x79b26a24: types.StoryItem,
    0x51e6ee4f: types.StoryItemDeleted,
    0xffadc913: types.StoryItemSkipped,
    0x6090d6d5: types.StoryReaction,
    0xbbab2643: types.StoryReactionPublicForward,
    0xcfcd0f13: types.StoryReactionPublicRepost,
    0xaa5f789c: types.stories.StoryReactionsList,
    0x50cd067c: types.stats.StoryStats,
    0xb0bdeac5: types.StoryView,
    0x9083670b: types.StoryViewPublicForward,
    0xbd74cf49: types.StoryViewPublicRepost,
    0x8d595cd6: types.StoryViews,
    0xde9eed1d: types.stories.StoryViews,
    0x59d78fc5: types.stories.StoryViewsList,
    0x4dafc503: functions.stickers.SuggestShortNameRequest,
    0x85fea03f: types.stickers.SuggestedShortName,
    0xb4d5d859: types.payments.SuggestedStarRefBots,
    0x17c6b5f6: types.help.Support,
    0x8c05f1c9: types.help.SupportName,
    0x4dba4501: types.account.Takeout,
    0x780a0310: types.help.TermsOfService,
    0x28ecf961: types.help.TermsOfServiceUpdate,
    0xe3309f7f: types.help.TermsOfServiceUpdateEmpty,
    0x35553762: types.TextAnchor,
    0x6724abc4: types.TextBold,
    0x7e6260d7: types.TextConcat,
    0xde5a0dd6: types.TextEmail,
    0xdc3d824f: types.TextEmpty,
    0x6c3f19b9: types.TextFixed,
    0x081ccf4f: types.TextImage,
    0xd912a59c: types.TextItalic,
    0x034b8621: types.TextMarked,
    0x1ccb966a: types.TextPhone,
    0x744694e0: types.TextPlain,
    0x9bf8bb95: types.TextStrike,
    0xed6a8504: types.TextSubscript,
    0xc7fb5e01: types.TextSuperscript,
    0xc12622c4: types.TextUnderline,
    0x3c2884c1: types.TextUrl,
    0x751f3146: types.TextWithEntities,
    0xa00e67d6: types.Theme,
    0xfa58b6d4: types.ThemeSettings,
    0x9a3d8c6d: types.account.Themes,
    0xf41eb622: types.account.ThemesNotModified,
    0xff9289f5: types.Timezone,
    0x7b74ed71: types.help.TimezonesList,
    0x970708cc: types.help.TimezonesListNotModified,
    0x10e8636f: types.TlsBlockDomain,
    0xe675a1c1: types.TlsBlockGrease,
    0x9eb95b5c: types.TlsBlockPublicKey,
    0x4d4dc41e: types.TlsBlockRandom,
    0xe725d44f: types.TlsBlockScope,
    0x4218a164: types.TlsBlockString,
    0x09333afb: types.TlsBlockZero,
    0x6c52c484: types.TlsClientHello,
    0xdb64fd34: types.account.TmpPassword,
    0x7c2557c4: functions.stories.ToggleAllStoriesHiddenRequest,
    0x68f3e4eb: functions.channels.ToggleAntiSpamRequest,
    0x69f59d69: functions.messages.ToggleBotInAttachMenuRequest,
    0x60eaefa1: functions.payments.ToggleChatStarGiftNotificationsRequest,
    0x646e1097: functions.account.ToggleConnectedBotPausedRequest,
    0xfd2dda49: functions.messages.ToggleDialogFilterTagsRequest,
    0xa731e257: functions.messages.ToggleDialogPinRequest,
    0xa4298b29: functions.channels.ToggleForumRequest,
    0xf128c708: functions.phone.ToggleGroupCallRecordRequest,
    0x74bbb43d: functions.phone.ToggleGroupCallSettingsRequest,
    0x219c34e6: functions.phone.ToggleGroupCallStartSubscriptionRequest,
    0x4c2985b6: functions.channels.ToggleJoinRequestRequest,
    0xe4cb9580: functions.channels.ToggleJoinToSendRequest,
    0xb11eafa2: functions.messages.ToggleNoForwardsRequest,
    0x435885b5: functions.messages.TogglePaidReactionPrivacyRequest,
    0x6a6e7854: functions.channels.ToggleParticipantsHiddenRequest,
    0xbd0415c4: functions.stories.TogglePeerStoriesHiddenRequest,
    0xe47cb579: functions.messages.TogglePeerTranslationsRequest,
    0x9a75a1ef: functions.stories.TogglePinnedRequest,
    0x0b297e9b: functions.stories.TogglePinnedToTopRequest,
    0xeabbb94c: functions.channels.TogglePreHistoryHiddenRequest,
    0xac81bbde: functions.messages.ToggleSavedDialogPinRequest,
    0x418d549c: functions.channels.ToggleSignaturesRequest,
    0xedd49ef0: functions.channels.ToggleSlowModeRequest,
    0xb9d9a38d: functions.account.ToggleSponsoredMessagesRequest,
    0x1513e7b0: functions.payments.ToggleStarGiftsPinnedToTopRequest,
    0xb5052fea: functions.messages.ToggleStickerSetsRequest,
    0x8514bdda: functions.contacts.ToggleTopPeersRequest,
    0x06de6392: functions.bots.ToggleUserEmojiStatusPermissionRequest,
    0x58d6b376: functions.account.ToggleUsernameRequest,
    0x50f24105: functions.channels.ToggleUsernameRequest,
    0x053ca973: functions.bots.ToggleUsernameRequest,
    0x9738bb15: functions.channels.ToggleViewForumAsMessagesRequest,
    0xedcdc05b: types.TopPeer,
    0xfd9e7bec: types.TopPeerCategoryBotsApp,
    0x148677e2: types.TopPeerCategoryBotsInline,
    0xab661b5b: types.TopPeerCategoryBotsPM,
    0x161d9628: types.TopPeerCategoryChannels,
    0x0637b7ed: types.TopPeerCategoryCorrespondents,
    0xfbeec0f0: types.TopPeerCategoryForwardChats,
    0xa8406ca9: types.TopPeerCategoryForwardUsers,
    0xbd17a14a: types.TopPeerCategoryGroups,
    0xfb834291: types.TopPeerCategoryPeers,
    0x1e76a78c: types.TopPeerCategoryPhoneCalls,
    0x70b772a8: types.contacts.TopPeers,
    0xb52c939d: types.contacts.TopPeersDisabled,
    0xde266ef5: types.contacts.TopPeersNotModified,
    0x269e9a49: functions.messages.TranscribeAudioRequest,
    0xcfb9d957: types.messages.TranscribedAudio,
    0x7f18176a: functions.payments.TransferStarGiftRequest,
    0x33db32f8: types.messages.TranslateResult,
    0x63183030: functions.messages.TranslateTextRequest,
    0xb550d328: functions.contacts.UnblockRequest,
    0xf96e55de: functions.messages.UninstallStickerSetRequest,
    0xcaa2f60b: types.payments.UniqueStarGift,
    0xee22b9a8: functions.messages.UnpinAllMessagesRequest,
    0x6a0d3206: functions.account.UnregisterDeviceRequest,
    0x17b7a20b: types.UpdateAttachMenuBots,
    0xec05b097: types.UpdateAutoSaveSettings,
    0xcc6e0c11: functions.account.UpdateBirthdayRequest,
    0x8ae5c97a: types.UpdateBotBusinessConnect,
    0xb9cfc48d: types.UpdateBotCallbackQuery,
    0x904dd49c: types.UpdateBotChatBoost,
    0x11dfa986: types.UpdateBotChatInviteRequester,
    0x4d712f2e: types.UpdateBotCommands,
    0xa02a982e: types.UpdateBotDeleteBusinessMessage,
    0x07df587c: types.UpdateBotEditBusinessMessage,
    0x496f379c: types.UpdateBotInlineQuery,
    0x12f12a07: types.UpdateBotInlineSend,
    0x14b85813: types.UpdateBotMenuButton,
    0xac21d3ce: types.UpdateBotMessageReaction,
    0x09cb7759: types.UpdateBotMessageReactions,
    0x9ddb347c: types.UpdateBotNewBusinessMessage,
    0x8caa9a96: types.UpdateBotPrecheckoutQuery,
    0x283bd312: types.UpdateBotPurchasedPaidMedia,
    0xb5aefd7d: types.UpdateBotShippingQuery,
    0xc4870a49: types.UpdateBotStopped,
    0x8317c0c3: types.UpdateBotWebhookJSON,
    0x9b9240a6: types.UpdateBotWebhookJSONQuery,
    0xdfd961f5: types.UpdateBroadcastRevenueTransactions,
    0xa26a7fa5: functions.account.UpdateBusinessAwayMessageRequest,
    0x1ea2fda7: types.UpdateBusinessBotCallbackQuery,
    0x66cdafc4: functions.account.UpdateBusinessGreetingMessageRequest,
    0xa614d034: functions.account.UpdateBusinessIntroRequest,
    0x9e6b131a: functions.account.UpdateBusinessLocationRequest,
    0x4b00e066: functions.account.UpdateBusinessWorkHoursRequest,
    0x635b4c09: types.UpdateChannel,
    0xb23fc698: types.UpdateChannelAvailableMessages,
    0xd29a27f4: types.UpdateChannelMessageForwards,
    0xf226ac08: types.UpdateChannelMessageViews,
    0x985d3abb: types.UpdateChannelParticipant,
    0x192efbe3: types.UpdateChannelPinnedTopic,
    0xfe198602: types.UpdateChannelPinnedTopics,
    0xea29055d: types.UpdateChannelReadMessagesContents,
    0x108d941f: types.UpdateChannelTooLong,
    0x8c88c923: types.UpdateChannelUserTyping,
    0x07b68920: types.UpdateChannelViewForumAsMessages,
    0x2f2ba99f: types.UpdateChannelWebPage,
    0xf89a6a4e: types.UpdateChat,
    0x54c01850: types.UpdateChatDefaultBannedRights,
    0xd087663a: types.UpdateChatParticipant,
    0x3dda5451: types.UpdateChatParticipantAdd,
    0xd7ca61a2: types.UpdateChatParticipantAdmin,
    0xe32f3d77: types.UpdateChatParticipantDelete,
    0x07761198: types.UpdateChatParticipants,
    0x83487af0: types.UpdateChatUserTyping,
    0x7cefa15d: functions.account.UpdateColorRequest,
    0xd8aa3671: functions.channels.UpdateColorRequest,
    0xa229dd06: types.UpdateConfig,
    0x66a08c7e: functions.account.UpdateConnectedBotRequest,
    0x7084a7be: types.UpdateContactsReset,
    0x8e5e9873: types.UpdateDcOptions,
    0xc32d5b12: types.UpdateDeleteChannelMessages,
    0xa20db0e5: types.UpdateDeleteMessages,
    0x53e6f1ec: types.UpdateDeleteQuickReply,
    0x566fe7cd: types.UpdateDeleteQuickReplyMessages,
    0xf2a71983: types.UpdateDeleteScheduledMessages,
    0x38df3532: functions.account.UpdateDeviceLockedRequest,
    0x26ffde7d: types.UpdateDialogFilter,
    0x1ad4a04a: functions.messages.UpdateDialogFilterRequest,
    0xa5d72105: types.UpdateDialogFilterOrder,
    0x3504914f: types.UpdateDialogFilters,
    0xc563c1e4: functions.messages.UpdateDialogFiltersOrderRequest,
    0x6e6fe51c: types.UpdateDialogPinned,
    0xe16459c3: types.UpdateDialogUnreadMark,
    0x1b49ec6d: types.UpdateDraftMessage,
    0x1b3f4df7: types.UpdateEditChannelMessage,
    0xe40370a3: types.UpdateEditMessage,
    0xfbd3de6b: functions.account.UpdateEmojiStatusRequest,
    0xf0d3e6a8: functions.channels.UpdateEmojiStatusRequest,
    0x1710f156: types.UpdateEncryptedChatTyping,
    0x38fe25b7: types.UpdateEncryptedMessagesRead,
    0xb4a2e88d: types.UpdateEncryption,
    0xe511996d: types.UpdateFavedStickers,
    0x19360dc0: types.UpdateFolderPeers,
    0x871fb939: types.UpdateGeoLiveViewed,
    0x97d64341: types.UpdateGroupCall,
    0x0b783982: types.UpdateGroupCallConnection,
    0xf2ebdb4e: types.UpdateGroupCallParticipants,
    0x691e9052: types.UpdateInlineBotCallbackQuery,
    0x56022f4d: types.UpdateLangPack,
    0x46560264: types.UpdateLangPackTooLong,
    0x564fe691: types.UpdateLoginToken,
    0xd5a41724: types.UpdateMessageExtendedMedia,
    0x4e90bfd6: types.UpdateMessageID,
    0xaca1657b: types.UpdateMessagePoll,
    0x24f40e77: types.UpdateMessagePollVote,
    0x5e1b3cb8: types.UpdateMessageReactions,
    0x86fccf85: types.UpdateMoveStickerSetToTop,
    0x8951abef: types.UpdateNewAuthorization,
    0x62ba04d9: types.UpdateNewChannelMessage,
    0x12bcbd9a: types.UpdateNewEncryptedMessage,
    0x1f2b0afd: types.UpdateNewMessage,
    0xf53da717: types.UpdateNewQuickReply,
    0x39a51dfb: types.UpdateNewScheduledMessage,
    0x688a30aa: types.UpdateNewStickerSet,
    0x1824e40b: types.UpdateNewStoryReaction,
    0xbec268ef: types.UpdateNotifySettings,
    0x84be5b93: functions.account.UpdateNotifySettingsRequest,
    0xfc84653f: functions.channels.UpdatePaidMessagesPriceRequest,
    0x8b725fce: types.UpdatePaidReactionPrivacy,
    0xa59b102f: functions.account.UpdatePasswordSettingsRequest,
    0xebe07752: types.UpdatePeerBlocked,
    0xbb9bb9a5: types.UpdatePeerHistoryTTL,
    0xb4afcfb0: types.UpdatePeerLocated,
    0x6a7e7366: types.UpdatePeerSettings,
    0xae3f101d: types.UpdatePeerWallpaper,
    0x7063c3db: types.UpdatePendingJoinRequests,
    0xd94305e0: functions.account.UpdatePersonalChannelRequest,
    0xab0f6b1e: types.UpdatePhoneCall,
    0x2661bf09: types.UpdatePhoneCallSignalingData,
    0x5bb98608: types.UpdatePinnedChannelMessages,
    0xfa0f3ca2: types.UpdatePinnedDialogs,
    0x6c2d9026: functions.channels.UpdatePinnedForumTopicRequest,
    0xd2aaf7ec: functions.messages.UpdatePinnedMessageRequest,
    0xed85eab5: types.UpdatePinnedMessages,
    0x686c85a6: types.UpdatePinnedSavedDialogs,
    0xee3b272a: types.UpdatePrivacy,
    0x78515775: functions.account.UpdateProfileRequest,
    0x09e82039: functions.photos.UpdateProfilePhotoRequest,
    0x3354678f: types.UpdatePtsChanged,
    0xf9470ab2: types.UpdateQuickReplies,
    0x3e050d0f: types.UpdateQuickReplyMessage,
    0xd6b19546: types.UpdateReadChannelDiscussionInbox,
    0x695c9e7c: types.UpdateReadChannelDiscussionOutbox,
    0x922e6e10: types.UpdateReadChannelInbox,
    0xb75f99a9: types.UpdateReadChannelOutbox,
    0xfb4c496c: types.UpdateReadFeaturedEmojiStickers,
    0x571d2742: types.UpdateReadFeaturedStickers,
    0x9c974fdf: types.UpdateReadHistoryInbox,
    0x2f2f21bf: types.UpdateReadHistoryOutbox,
    0xf8227181: types.UpdateReadMessagesContents,
    0xf74e932b: types.UpdateReadStories,
    0x30f443db: types.UpdateRecentEmojiStatuses,
    0x6f7863f4: types.UpdateRecentReactions,
    0x9a422c20: types.UpdateRecentStickers,
    0xaeaf9e74: types.UpdateSavedDialogPinned,
    0x9375341e: types.UpdateSavedGifs,
    0x60297dec: functions.messages.UpdateSavedReactionTagRequest,
    0x39c67432: types.UpdateSavedReactionTags,
    0x74d8be99: types.UpdateSavedRingtones,
    0x504aa18f: types.UpdateSentPhoneCode,
    0x7d627683: types.UpdateSentStoryReaction,
    0xebe46819: types.UpdateServiceNotification,
    0x093fa0bf: functions.smsjobs.UpdateSettingsRequest,
    0x78d4dec1: types.UpdateShort,
    0x4d6deea5: types.UpdateShortChatMessage,
    0x313bc7f8: types.UpdateShortMessage,
    0x9015e101: types.UpdateShortSentMessage,
    0xf16269d4: types.UpdateSmsJob,
    0x778b5ab3: functions.bots.UpdateStarRefProgramRequest,
    0x4e80a379: types.UpdateStarsBalance,
    0xa584b019: types.UpdateStarsRevenueStatus,
    0x6628562c: functions.account.UpdateStatusRequest,
    0x31c24808: types.UpdateStickerSets,
    0x0bb2d201: types.UpdateStickerSetsOrder,
    0x2c084dc1: types.UpdateStoriesStealthMode,
    0x75b3b798: types.UpdateStory,
    0x1bf335b9: types.UpdateStoryID,
    0x8216fba3: types.UpdateTheme,
    0x2bf40ccc: functions.account.UpdateThemeRequest,
    0x0084cd5a: types.UpdateTranscribedAudio,
    0x20529438: types.UpdateUser,
    0x28373599: types.UpdateUserEmojiStatus,
    0xed9f30c5: functions.bots.UpdateUserEmojiStatusRequest,
    0xa7848924: types.UpdateUserName,
    0x05492a13: types.UpdateUserPhone,
    0xe5bdf8de: types.UpdateUserStatus,
    0xc01e857f: types.UpdateUserTyping,
    0x3e0bdd7c: functions.account.UpdateUsernameRequest,
    0x3514b3de: functions.channels.UpdateUsernameRequest,
    0x7f891213: types.UpdateWebPage,
    0x1592b79d: types.UpdateWebViewResultSent,
    0x74ae4240: types.Updates,
    0x725b04c3: types.UpdatesCombined,
    0xe317af7e: types.UpdatesTooLong,
    0xaed6e4f5: functions.payments.UpgradeStarGiftRequest,
    0xe14c4a71: functions.photos.UploadContactProfilePhotoRequest,
    0x5057c497: functions.messages.UploadEncryptedFileRequest,
    0x2a862092: functions.messages.UploadImportedMediaRequest,
    0x14967978: functions.messages.UploadMediaRequest,
    0x0388a3b5: functions.photos.UploadProfilePhotoRequest,
    0x831a83a2: functions.account.UploadRingtoneRequest,
    0x1c3db333: functions.account.UploadThemeRequest,
    0xe39a8f03: functions.account.UploadWallPaperRequest,
    0x8f8c0e4e: types.UrlAuthResultAccepted,
    0xa9d6db1f: types.UrlAuthResultDefault,
    0x92d33a0e: types.UrlAuthResultRequest,
    0x020b1422: types.User,
    0xd3bc4b7a: types.UserEmpty,
    0x99e78045: types.UserFull,
    0x3b6d152e: types.users.UserFull,
    0x01eb3758: types.help.UserInfo,
    0xf3ae2eed: types.help.UserInfoEmpty,
    0x82d1f706: types.UserProfilePhoto,
    0x4f11bae1: types.UserProfilePhotoEmpty,
    0x09d05049: types.UserStatusEmpty,
    0x65899777: types.UserStatusLastMonth,
    0x541a1d1a: types.UserStatusLastWeek,
    0x008c703f: types.UserStatusOffline,
    0xedb93949: types.UserStatusOnline,
    0x7b197dc8: types.UserStatusRecently,
    0xb4073647: types.Username,
    0x62d706b8: types.users.Users,
    0x315a4974: types.users.UsersSlice,
    0xb6c8f12b: functions.payments.ValidateRequestedInfoRequest,
    0xd1451883: types.payments.ValidatedRequestedInfo,
    0x032da4cf: functions.account.VerifyEmailRequest,
    0x4dd3a7f6: functions.account.VerifyPhoneRequest,
    0xde33b094: types.VideoSize,
    0xf85c413c: types.VideoSizeEmojiMarkup,
    0x0da082fe: types.VideoSizeStickerMarkup,
    0x269e3643: functions.messages.ViewSponsoredMessageRequest,
    0x4899484e: types.messages.VotesList,
    0xa437c3ed: types.WallPaper,
    0xe0804116: types.WallPaperNoFile,
    0x372efcd0: types.WallPaperSettings,
    0xcdc3858c: types.account.WallPapers,
    0x1c199183: types.account.WallPapersNotModified,
    0xa6f8f452: types.WebAuthorization,
    0xed56c9fc: types.account.WebAuthorizations,
    0x1c570ed1: types.WebDocument,
    0xf9c8bcc6: types.WebDocumentNoProxy,
    0x21e753bc: types.upload.WebFile,
    0xe89c45b2: types.WebPage,
    0xfd5e12bd: types.messages.WebPage,
    0x50cc03d3: types.WebPageAttributeStickerSet,
    0x2e94c3e7: types.WebPageAttributeStory,
    0x54b56617: types.WebPageAttributeTheme,
    0xcf6f6db8: types.WebPageAttributeUniqueStarGift,
    0x211a1788: types.WebPageEmpty,
    0x7311ca11: types.WebPageNotModified,
    0xb0d13e47: types.WebPagePending,
    0xb53e8b21: types.messages.WebPagePreview,
    0x0c94511c: types.WebViewMessageSent,
    0x4d22ff98: types.WebViewResultUrl,
}
