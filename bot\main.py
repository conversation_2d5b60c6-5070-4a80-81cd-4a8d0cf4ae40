import logging
from telegram.ext import Application, Command<PERSON><PERSON><PERSON>, MessageHandler, filters
from telegram import Update
import sqlite3
import os
from datetime import datetime
import time
import asyncio
from dotenv import load_dotenv

# Настройка логирования
logging.basicConfig(format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", level=logging.INFO)
logger = logging.getLogger(__name__)

# Загружаем переменные окружения
load_dotenv("config.env")

# Инициализация монитора скорости (опционально)
try:
    from speed_monitor import SpeedMonitor
    speed_monitor = SpeedMonitor()
    HAS_SPEED_MONITOR = True
except ImportError:
    logger.warning("speed_monitor не найден, статистика скорости будет недоступна")
    speed_monitor = None
    HAS_SPEED_MONITOR = False

# Настройки из переменных окружения
ALLOWED_USER_ID = os.getenv("TG_ALLOWED_USER_ID")  # Опционально
CHANNEL_ID = os.getenv("TG_DEST_CHAT")
BOT_TOKEN = os.getenv("TG_BOT_TOKEN")

# Подключение к базе данных SQLite
DB_PATH = "db/files.db"

def init_db():
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_name TEXT NOT NULL,
            file_id TEXT NOT NULL,
            upload_date TEXT NOT NULL
        )
    ''')
    conn.commit()
    conn.close()

async def start(update: Update, context):
    user_id = update.effective_user.id
    if ALLOWED_USER_ID and str(user_id) != str(ALLOWED_USER_ID):
        await update.message.reply_text("Извините, у вас нет доступа к этому боту.")
        return
    await update.message.reply_text("Привет! Я бот для хранения файлов в Telegram. Используйте /upload для загрузки файлов, /list для просмотра списка файлов и /download <id> для скачивания.")

async def upload(update: Update, context):
    user_id = update.effective_user.id
    if ALLOWED_USER_ID and str(user_id) != str(ALLOWED_USER_ID):
        await update.message.reply_text("Извините, у вас нет доступа к этому боту.")
        return
    
    if not update.message.document:
        await update.message.reply_text("Пожалуйста, отправьте файл для загрузки.")
        return
    
    file = update.message.document
    file_name = file.file_name
    file_id = file.file_id
    file_size = file.file_size  # Размер файла в байтах
    upload_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Начинаем измерение времени загрузки
    start_time = time.time()
    
    try:
        # Сохраняем файл в канал
        await context.bot.send_document(chat_id=CHANNEL_ID, document=file_id, caption=file_name)
        
        # Заканчиваем измерение времени
        end_time = time.time()
        upload_duration = end_time - start_time
        
        # Вычисляем скорость загрузки
        if upload_duration > 0:
            speed_bps = file_size / upload_duration  # байт в секунду
            speed_kbps = speed_bps / 1024  # килобайт в секунду
            speed_mbps = speed_kbps / 1024  # мегабайт в секунду
        else:
            speed_bps = speed_kbps = speed_mbps = 0
        
        # Сохраняем статистику в мониторе (если доступен)
        if HAS_SPEED_MONITOR and speed_monitor:
            speed_monitor.save_upload_stats(file_name, file_size, upload_duration, speed_mbps)
        
        # Сохраняем метаданные в базу данных
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("INSERT INTO files (file_name, file_id, upload_date) VALUES (?, ?, ?)", (file_name, file_id, upload_date))
        conn.commit()
        file_db_id = cursor.lastrowid
        conn.close()
        
        # Формируем ответ с информацией о скорости
        size_mb = file_size / (1024 * 1024)
        response = f"✅ Файл '{file_name}' успешно загружен!\n"
        response += f"📋 ID файла: {file_db_id}\n"
        response += f"📦 Размер: {size_mb:.2f} МБ\n"
        response += f"⏱ Время загрузки: {upload_duration:.2f} сек\n"
        
        if speed_mbps >= 1:
            response += f"🚀 Скорость: {speed_mbps:.2f} МБ/с"
        elif speed_kbps >= 1:
            response += f"🚀 Скорость: {speed_kbps:.2f} КБ/с"
        else:
            response += f"🚀 Скорость: {speed_bps:.2f} Б/с"
        
        await update.message.reply_text(response)
        
        # Логируем статистику
        logger.info(f"Файл загружен: {file_name}, размер: {size_mb:.2f} МБ, время: {upload_duration:.2f}с, скорость: {speed_mbps:.2f} МБ/с")
        
    except Exception as e:
        logger.error(f"Ошибка при загрузке файла: {e}")
        await update.message.reply_text(f"❌ Ошибка при загрузке файла: {str(e)}")

async def list_files(update: Update, context):
    user_id = update.effective_user.id
    if ALLOWED_USER_ID and str(user_id) != str(ALLOWED_USER_ID):
        await update.message.reply_text("Извините, у вас нет доступа к этому боту.")
        return
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT id, file_name, upload_date FROM files ORDER BY upload_date DESC")
    files = cursor.fetchall()
    conn.close()
    
    if not files:
        await update.message.reply_text("Нет загруженных файлов.")
        return
    
    response = "Список файлов:\n"
    for file in files:
        response += f"ID: {file[0]} | Название: {file[1]} | Дата загрузки: {file[2]}\n"
    await update.message.reply_text(response)

async def download(update: Update, context):
    user_id = update.effective_user.id
    if ALLOWED_USER_ID and str(user_id) != str(ALLOWED_USER_ID):
        await update.message.reply_text("Извините, у вас нет доступа к этому боту.")
        return
    
    if not context.args:
        await update.message.reply_text("Пожалуйста, укажите ID файла для скачивания. Пример: /download 1")
        return
    
    try:
        file_db_id = int(context.args[0])
    except ValueError:
        await update.message.reply_text("ID файла должен быть числом.")
        return
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT file_name, file_id FROM files WHERE id = ?", (file_db_id,))
    file_data = cursor.fetchone()
    conn.close()
    
    if not file_data:
        await update.message.reply_text(f"Файл с ID {file_db_id} не найден.")
        return
    
    file_name, file_id = file_data
    await context.bot.send_document(chat_id=update.effective_chat.id, document=file_id, caption=f"Скачивание: {file_name}")

# Добавляем новую команду для просмотра статистики
async def speed_stats(update: Update, context):
    user_id = update.effective_user.id
    if ALLOWED_USER_ID and str(user_id) != str(ALLOWED_USER_ID):
        await update.message.reply_text("Извините, у вас нет доступа к этому боту.")
        return

    if not HAS_SPEED_MONITOR or not speed_monitor:
        await update.message.reply_text("📊 Статистика скорости недоступна (модуль speed_monitor не найден)")
        return

    # Получаем статистику за разные периоды
    stats_24h = speed_monitor.get_speed_statistics(24)
    stats_7d = speed_monitor.get_speed_statistics(24 * 7)
    
    response = "📊 **Статистика скорости загрузки**\n\n"
    
    if "error" not in stats_24h:
        response += "**За последние 24 часа:**\n"
        response += f"• Загрузок: {stats_24h['количество_загрузок']}\n"
        response += f"• Средняя скорость: {stats_24h['средняя_скорость_мбс']} МБ/с\n"
        response += f"• Максимальная скорость: {stats_24h['максимальная_скорость_мбс']} МБ/с\n"
        response += f"• Общий объем: {stats_24h['общий_объем_мб']} МБ\n\n"
    
    if "error" not in stats_7d:
        response += "**За последние 7 дней:**\n"
        response += f"• Загрузок: {stats_7d['количество_загрузок']}\n"
        response += f"• Средняя скорость: {stats_7d['средняя_скорость_мбс']} МБ/с\n"
        response += f"• Общий объем: {stats_7d['общий_объем_мб']} МБ\n"
    
    if "error" in stats_24h and "error" in stats_7d:
        response = "📊 Нет данных о скорости загрузки"
    
    await update.message.reply_text(response, parse_mode='Markdown')

def main():
    # Проверяем наличие токена бота
    if not BOT_TOKEN:
        logger.error("TG_BOT_TOKEN не найден в config.env!")
        return

    if not CHANNEL_ID:
        logger.error("TG_DEST_CHAT не найден в config.env!")
        return

    # Инициализация базы данных
    init_db()

    # Создание приложения Telegram
    application = Application.builder().token(BOT_TOKEN).build()
    
    # Добавление обработчиков команд
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("upload", upload))
    application.add_handler(CommandHandler("list", list_files))
    application.add_handler(CommandHandler("download", download))
    application.add_handler(CommandHandler("stats", speed_stats))
    application.add_handler(MessageHandler(filters.Document.ALL, upload))
    
    # Запуск бота
    logger.info("Запуск бота...")
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == "__main__":
    main()
