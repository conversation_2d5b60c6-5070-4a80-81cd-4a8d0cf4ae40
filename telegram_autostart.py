#!/usr/bin/env python3
"""
Настройка автозапуска Telegram WebDAV-сервера при загрузке Windows 11
"""

import os
import sys
import winreg
import subprocess
from pathlib import Path
import shutil

def get_project_paths():
    """Получает пути к проекту"""
    current_dir = Path(__file__).parent.resolve()
    venv_python = current_dir / "venv" / "Scripts" / "python.exe"
    webdav_script = current_dir / "telegram_webdav.py"
    
    return {
        'project_dir': current_dir,
        'venv_python': venv_python,
        'webdav_script': webdav_script,
        'startup_folder': Path.home() / "AppData" / "Roaming" / "Microsoft" / "Windows" / "Start Menu" / "Programs" / "Startup"
    }

def create_startup_batch():
    """Создает batch-файл для запуска в папке автозагрузки"""
    paths = get_project_paths()
    
    batch_content = f'''@echo off
title Telegram WebDAV Server
chcp 65001 >nul
cd /d "{paths['project_dir']}"

echo Запуск Telegram WebDAV-сервера...
echo Проект: {paths['project_dir']}
echo Время: %date% %time%

REM Проверяем наличие файлов
if not exist "{paths['venv_python']}" (
    echo ОШИБКА: Python venv не найден!
    echo Путь: {paths['venv_python']}
    pause
    exit /b 1
)

if not exist "{paths['webdav_script']}" (
    echo ОШИБКА: Скрипт WebDAV не найден!
    echo Путь: {paths['webdav_script']}
    pause
    exit /b 1
)

REM Запускаем WebDAV-сервер
echo Запуск WebDAV-сервера...
"{paths['venv_python']}" "{paths['webdav_script']}"

REM Если сервер остановился
echo.
echo WebDAV-сервер остановлен.
echo Нажмите любую клавишу для выхода...
pause > nul
'''
    
    startup_batch = paths['startup_folder'] / "TelegramWebDAV.bat"
    
    try:
        # Создаем папку автозагрузки если её нет
        paths['startup_folder'].mkdir(parents=True, exist_ok=True)
        
        # Записываем batch-файл
        with open(startup_batch, 'w', encoding='cp1251') as f:
            f.write(batch_content)
        
        print(f"✅ Создан startup batch: {startup_batch}")
        return startup_batch
        
    except Exception as e:
        print(f"❌ Ошибка создания batch: {e}")
        return None

def create_vbs_hidden_launcher():
    """Создает VBS-скрипт для скрытого запуска (без окна)"""
    paths = get_project_paths()
    
    vbs_content = f'''Set WshShell = CreateObject("WScript.Shell")
WshShell.Run chr(34) & "{paths['startup_folder'] / 'TelegramWebDAV.bat'}" & chr(34), 0
Set WshShell = Nothing
'''
    
    vbs_file = paths['startup_folder'] / "TelegramWebDAV_Hidden.vbs"
    
    try:
        with open(vbs_file, 'w', encoding='utf-8') as f:
            f.write(vbs_content)
        
        print(f"✅ Создан VBS-лаунчер: {vbs_file}")
        return vbs_file
        
    except Exception as e:
        print(f"❌ Ошибка создания VBS: {e}")
        return None

def add_to_registry():
    """Добавляет запуск через реестр Windows"""
    paths = get_project_paths()
    
    # Команда для запуска
    command = f'"{paths["venv_python"]}" "{paths["webdav_script"]}"'
    
    try:
        # Открываем ключ автозапуска
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Run",
            0,
            winreg.KEY_SET_VALUE
        )
        
        # Добавляем значение
        winreg.SetValueEx(key, "TelegramWebDAV", 0, winreg.REG_SZ, command)
        winreg.CloseKey(key)
        
        print("✅ Добавлено в реестр автозапуска")
        print(f"   Команда: {command}")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка добавления в реестр: {e}")
        return False

def create_windows_service():
    """Создает Windows Service (требует прав администратора)"""
    paths = get_project_paths()
    
    service_script = f'''import win32serviceutil
import win32service
import win32event
import subprocess
import sys
import os

class TelegramWebDAVService(win32serviceutil.ServiceFramework):
    _svc_name_ = "TelegramWebDAV"
    _svc_display_name_ = "Telegram WebDAV Server"
    _svc_description_ = "WebDAV-сервер для доступа к Telegram как облачному хранилищу"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.process = None
    
    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        if self.process:
            self.process.terminate()
        win32event.SetEvent(self.hWaitStop)
    
    def SvcDoRun(self):
        # Меняем рабочую директорию
        os.chdir(r"{paths['project_dir']}")
        
        # Запускаем WebDAV-сервер
        self.process = subprocess.Popen([
            r"{paths['venv_python']}",
            r"{paths['webdav_script']}"
        ])
        
        # Ждем сигнала остановки
        win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(TelegramWebDAVService)
'''
    
    service_file = paths['project_dir'] / "telegram_service.py"
    
    try:
        with open(service_file, 'w', encoding='utf-8') as f:
            f.write(service_script)
        
        print(f"✅ Создан файл службы: {service_file}")
        print("⚠️  Для установки службы нужны права администратора!")
        print("   Команды установки:")
        print(f"   python {service_file} install")
        print(f"   python {service_file} start")
        
        return service_file
        
    except Exception as e:
        print(f"❌ Ошибка создания службы: {e}")
        return None

def create_task_scheduler():
    """Создает задачу в планировщике Windows"""
    paths = get_project_paths()
    
    try:
        # Простой способ - создаем задачу через параметры командной строки
        print("🔧 Создание задачи в планировщике Windows...")
        
        # Удаляем существующую задачу если есть
        subprocess.run(['schtasks', '/delete', '/tn', 'TelegramWebDAV', '/f'], 
                      capture_output=True, encoding='cp866', errors='ignore')
        
        # Создаем новую задачу
        create_cmd = [
            'schtasks', '/create',
            '/tn', 'TelegramWebDAV',
            '/tr', f'"{paths["venv_python"]}" "{paths["webdav_script"]}"',
            '/sc', 'onlogon',
            '/ru', os.getenv('USERNAME'),
            '/rl', 'limited',
            '/f'  # принудительно перезаписать
        ]
        
        result = subprocess.run(create_cmd, capture_output=True, text=True, 
                              encoding='cp866', errors='ignore')
        
        if result.returncode == 0:
            print("✅ Задача добавлена в планировщик Windows")
            print("   Название: TelegramWebDAV")
            print("   Триггер: При входе в систему")
            print("   Пользователь:", os.getenv('USERNAME'))
            
            # Проверяем созданную задачу
            check_cmd = ['schtasks', '/query', '/tn', 'TelegramWebDAV']
            check_result = subprocess.run(check_cmd, capture_output=True, text=True,
                                        encoding='cp866', errors='ignore')
            if check_result.returncode == 0:
                print("✅ Задача успешно создана и найдена в планировщике")
            return True
        else:
            print(f"❌ Ошибка создания задачи:")
            print(f"Код ошибки: {result.returncode}")
            if result.stderr:
                print(f"Stderr: {result.stderr}")
            if result.stdout:
                print(f"Stdout: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"❌ Исключение при создании задачи: {e}")
        return False

def remove_autostart():
    """Удаляет все варианты автозапуска"""
    paths = get_project_paths()
    removed = []
    
    # Удаляем из папки автозагрузки
    for file in ['TelegramWebDAV.bat', 'TelegramWebDAV_Hidden.vbs']:
        file_path = paths['startup_folder'] / file
        if file_path.exists():
            file_path.unlink()
            removed.append(f"Startup: {file}")
    
    # Удаляем из реестра
    try:
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Run",
            0,
            winreg.KEY_SET_VALUE
        )
        winreg.DeleteValue(key, "TelegramWebDAV")
        winreg.CloseKey(key)
        removed.append("Реестр автозапуска")
    except FileNotFoundError:
        pass  # Значение не существует
    except Exception as e:
        print(f"⚠️  Ошибка удаления из реестра: {e}")
    
    # Удаляем задачу планировщика
    try:
        result = subprocess.run(['schtasks', '/delete', '/tn', 'TelegramWebDAV', '/f'], 
                              capture_output=True, encoding='cp866', errors='ignore')
        if result.returncode == 0:
            removed.append("Планировщик задач")
    except Exception:
        pass
    
    if removed:
        print(f"✅ Удалены автозапуски: {', '.join(removed)}")
    else:
        print("ℹ️  Автозапуски не найдены")

def main():
    """Главное меню настройки автозапуска"""
    print("🚀 Настройка автозапуска Telegram WebDAV для Windows 11")
    print("=" * 60)
    
    # Проверяем наличие файлов
    paths = get_project_paths()
    
    if not paths['venv_python'].exists():
        print(f"❌ Python venv не найден: {paths['venv_python']}")
        print("   Запустите: python setup.py")
        return
    
    if not paths['webdav_script'].exists():
        print(f"❌ WebDAV скрипт не найден: {paths['webdav_script']}")
        return
    
    print("📁 Проект найден:")
    print(f"   Папка: {paths['project_dir']}")
    print(f"   Python: {paths['venv_python']}")
    print(f"   Скрипт: {paths['webdav_script']}")
    print()
    
    while True:
        print("Выберите метод автозапуска:")
        print("1. 📂 Папка автозагрузки (простой)")
        print("2. 🔇 Скрытый запуск через VBS")
        print("3. 🗂️  Реестр Windows")
        print("4. ⏰ Планировщик задач (рекомендуется)")
        print("5. 🛠️  Windows Service (требует admin)")
        print("6. 🗑️  Удалить все автозапуски")
        print("0. ❌ Выход")
        
        choice = input("\nВаш выбор (1-6, 0): ").strip()
        
        if choice == '1':
            create_startup_batch()
        elif choice == '2':
            create_startup_batch()
            create_vbs_hidden_launcher()
        elif choice == '3':
            add_to_registry()
        elif choice == '4':
            create_task_scheduler()
        elif choice == '5':
            create_windows_service()
        elif choice == '6':
            remove_autostart()
        elif choice == '0':
            break
        else:
            print("❌ Неверный выбор!")
            continue
        
        print("\n" + "─" * 40)

if __name__ == "__main__":
    main() 