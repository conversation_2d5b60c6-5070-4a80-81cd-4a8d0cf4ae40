# TelegramCloudLauncher.ps1
# ================================
# Запуск и монтирование Telegram облачного диска одним кликом
# Поддержка Windows 10/11 с PowerShell 5.1+

param(
    [string]$DriveLetter = "T",
    [switch]$Silent = $false,
    [switch]$AutoOpen = $false,
    [switch]$CreateShortcut = $false
)

# Настройки консоли
$Host.UI.RawUI.WindowTitle = "Telegram Cloud Launcher"
if (-not $Silent) {
    Clear-Host
    Write-Host @"
████████╗███████╗██╗     ███████╗ ██████╗ ██████╗  █████╗ ███╗   ███╗
╚══██╔══╝██╔════╝██║     ██╔════╝██╔════╝ ██╔══██╗██╔══██╗████╗ ████║
   ██║   █████╗  ██║     █████╗  ██║  ███╗██████╔╝███████║██╔████╔██║
   ██║   ██╔══╝  ██║     ██╔══╝  ██║   ██║██╔══██╗██╔══██║██║╚██╔╝██║
   ██║   ███████╗███████╗███████╗╚██████╔╝██║  ██║██║  ██║██║ ╚═╝ ██║
   ╚═╝   ╚══════╝╚══════╝╚══════╝ ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝     ╚═╝

                    🚀 ОБЛАЧНЫЙ ДИСК ТЕЛЕГРАМ 🚀
                     PowerShell Launcher v1.0
═══════════════════════════════════════════════════════════════════════
"@ -ForegroundColor Green
}

# Глобальные переменные
$ProjectDir = $PSScriptRoot
$ConfigPath = Join-Path $ProjectDir "config.env"
$VenvPath = Join-Path $ProjectDir "venv"
$WebDAVProcess = $null
$MountProcess = $null

# Функции логирования
function Write-Success($Message) {
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Warning($Message) {
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Write-Error($Message) {
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info($Message) {
    Write-Host "ℹ️  $Message" -ForegroundColor Cyan
}

# Проверка прав администратора
function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Получение доступных букв дисков
function Get-AvailableDriveLetters {
    $usedDrives = Get-PSDrive -PSProvider FileSystem | Select-Object -ExpandProperty Name
    $preferredLetters = @('T', 'G', 'H', 'X', 'Y', 'Z')
    
    foreach ($letter in $preferredLetters) {
        if ($letter -notin $usedDrives) {
            return $letter
        }
    }
    
    # Если предпочтительные заняты, ищем любую свободную
    $allLetters = 65..90 | ForEach-Object { [char]$_ }
    foreach ($letter in $allLetters) {
        if ($letter -notin $usedDrives -and $letter -ne 'C' -and $letter -ne 'A' -and $letter -ne 'B') {
            return $letter
        }
    }
    
    return $null
}

# Проверка виртуального окружения
function Test-VirtualEnvironment {
    $activateScript = Join-Path $VenvPath "Scripts\Activate.ps1"
    return Test-Path $activateScript
}

# Активация виртуального окружения
function Enable-VirtualEnvironment {
    if (Test-VirtualEnvironment) {
        $activateScript = Join-Path $VenvPath "Scripts\Activate.ps1"
        & $activateScript
        Write-Success "Виртуальное окружение активировано"
        return $true
    } else {
        Write-Error "Виртуальное окружение не найдено"
        return $false
    }
}

# Проверка WinFsp
function Test-WinFsp {
    $winfspPaths = @(
        "C:\Program Files\WinFsp\bin\winfsp-x64.dll",
        "C:\Program Files (x86)\WinFsp\bin\winfsp-x64.dll"
    )
    
    foreach ($path in $winfspPaths) {
        if (Test-Path $path) {
            return $true
        }
    }
    return $false
}

# Проверка rclone
function Test-Rclone {
    try {
        $null = Get-Command rclone -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Проверка конфигурации rclone
function Test-RcloneConfig {
    $rcloneConfigPath = Join-Path $env:USERPROFILE ".config\rclone\rclone.conf"
    return Test-Path $rcloneConfigPath
}

# Проверка WebDAV сервера
function Test-WebDAVServer {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080" -Method GET -TimeoutSec 5 -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Запуск WebDAV сервера
function Start-WebDAVServer {
    Write-Info "Запуск WebDAV сервера..."
    
    $webdavScript = Join-Path $ProjectDir "telegram_webdav.py"
    if (-not (Test-Path $webdavScript)) {
        Write-Error "telegram_webdav.py не найден"
        return $false
    }
    
    try {
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = "python"
        $processInfo.Arguments = "`"$webdavScript`""
        $processInfo.WorkingDirectory = $ProjectDir
        $processInfo.UseShellExecute = $false
        $processInfo.RedirectStandardOutput = $true
        $processInfo.RedirectStandardError = $true
        $processInfo.CreateNoWindow = $true
        
        $script:WebDAVProcess = [System.Diagnostics.Process]::Start($processInfo)
        
        # Ждем запуска сервера
        for ($i = 1; $i -le 30; $i++) {
            Start-Sleep -Seconds 1
            if (Test-WebDAVServer) {
                Write-Success "WebDAV сервер запущен на http://localhost:8080"
                return $true
            }
            Write-Host "." -NoNewline
        }
        
        Write-Error "WebDAV сервер не запустился за 30 секунд"
        return $false
        
    } catch {
        Write-Error "Ошибка запуска WebDAV сервера: $_"
        return $false
    }
}

# Монтирование диска
function Mount-TelegramDrive {
    param([string]$Letter)
    
    Write-Info "Монтирование диска $Letter`:..."
    
    try {
        $mountPoint = "$Letter`:"
        $arguments = @(
            "mount", "telegram:", $mountPoint,
            "--vfs-cache-mode", "writes",
            "--vfs-cache-max-size", "1G",
            "--vfs-cache-max-age", "1h",
            "--dir-cache-time", "5m",
            "--poll-interval", "10s",
            "--timeout", "30s",
            "--stats", "30s",
            "--log-level", "INFO"
        )
        
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = "rclone"
        $processInfo.Arguments = $arguments -join " "
        $processInfo.UseShellExecute = $false
        $processInfo.RedirectStandardOutput = $true
        $processInfo.RedirectStandardError = $true
        $processInfo.CreateNoWindow = $true
        
        $script:MountProcess = [System.Diagnostics.Process]::Start($processInfo)
        
        # Ждем монтирования
        for ($i = 1; $i -le 15; $i++) {
            Start-Sleep -Seconds 1
            if (Test-Path "$mountPoint\") {
                Write-Success "Диск $Letter`: успешно смонтирован"
                return $true
            }
            Write-Host "." -NoNewline
        }
        
        Write-Warning "Диск может монтироваться в фоне..."
        return $true
        
    } catch {
        Write-Error "Ошибка монтирования диска: $_"
        return $false
    }
}

# Размонтирование диска
function Dismount-TelegramDrive {
    Write-Info "Размонтирование диска..."
    
    if ($script:MountProcess -and -not $script:MountProcess.HasExited) {
        $script:MountProcess.Kill()
        $script:MountProcess = $null
    }
    
    if ($script:WebDAVProcess -and -not $script:WebDAVProcess.HasExited) {
        $script:WebDAVProcess.Kill()
        $script:WebDAVProcess = $null
    }
    
    Write-Success "Диск размонтирован"
}

# Создание ярлыка на рабочем столе
function New-DesktopShortcut {
    param([string]$DriveLetter)
    
    try {
        $shortcutPath = Join-Path $env:USERPROFILE "Desktop\Telegram Cloud $DriveLetter.lnk"
        $wshShell = New-Object -ComObject WScript.Shell
        $shortcut = $wshShell.CreateShortcut($shortcutPath)
        $shortcut.TargetPath = "$DriveLetter`:\"
        $shortcut.IconLocation = "$env:windir\system32\imageres.dll,137"
        $shortcut.Description = "Telegram облачный диск"
        $shortcut.Save()
        
        Write-Success "Ярлык создан на рабочем столе: $shortcutPath"
        return $true
    } catch {
        Write-Error "Ошибка создания ярлыка: $_"
        return $false
    }
}

# Главная функция
function Main {
    try {
        Set-Location $ProjectDir
        
        if (-not $Silent) {
            Write-Info "Рабочая директория: $ProjectDir"
            Write-Info "Время запуска: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
            Write-Host ""
        }
        
        # Проверка виртуального окружения
        Write-Info "Проверка виртуального окружения..."
        if (-not (Enable-VirtualEnvironment)) {
            Write-Error "Запустите setup.py для первоначальной настройки"
            return 1
        }
        
        # Проверка конфигурации
        Write-Info "Проверка конфигурации..."
        if (-not (Test-Path $ConfigPath)) {
            $exampleConfig = Join-Path $ProjectDir "config_example.env"
            if (Test-Path $exampleConfig) {
                Copy-Item $exampleConfig $ConfigPath
                Write-Warning "Создан config.env из примера. Отредактируйте файл и укажите ваши данные!"
                Start-Process notepad $ConfigPath
                return 1
            } else {
                Write-Error "Файл конфигурации не найден"
                return 1
            }
        }
        
        # Проверка зависимостей
        Write-Info "Проверка зависимостей..."
        
        if (-not (Test-Path (Join-Path $ProjectDir "telegram_webdav.py"))) {
            Write-Error "telegram_webdav.py не найден"
            return 1
        }
        
        if (-not (Test-WinFsp)) {
            Write-Warning "WinFsp не установлен. Запустите install_winfsp.py"
            return 1
        }
        
        if (-not (Test-Rclone)) {
            Write-Warning "rclone не найден. Запустите install_rclone.py"
            return 1
        }
        
        if (-not (Test-RcloneConfig)) {
            Write-Warning "Конфигурация rclone не найдена. Запустите rclone_setup.py"
            return 1
        }
        
        # Получение буквы диска
        if (-not $DriveLetter -or $DriveLetter.Length -ne 1) {
            $DriveLetter = Get-AvailableDriveLetters
            if (-not $DriveLetter) {
                Write-Error "Нет доступных букв дисков"
                return 1
            }
        }
        
        Write-Info "Используется диск: $DriveLetter`:"
        Write-Host ""
        
        # Запуск WebDAV сервера
        if (-not (Start-WebDAVServer)) {
            return 1
        }
        
        # Монтирование диска
        if (-not (Mount-TelegramDrive -Letter $DriveLetter)) {
            return 1
        }
        
        Write-Host ""
        Write-Host "═══════════════════════════════════════════════════════════════════════" -ForegroundColor Green
        Write-Host "                              🎉 ГОТОВО!" -ForegroundColor Green
        Write-Host "═══════════════════════════════════════════════════════════════════════" -ForegroundColor Green
        Write-Host ""
        Write-Success "Telegram облачный диск готов к работе!"
        Write-Host ""
        Write-Host "📁 Диск доступен по адресу: $DriveLetter`:\" -ForegroundColor Cyan
        Write-Host "🌐 WebDAV сервер: http://localhost:8080" -ForegroundColor Cyan
        Write-Host "💻 Откройте Проводник и перейдите в 'Этот компьютер'" -ForegroundColor Cyan
        Write-Host ""
        
        # Автоматические действия
        if ($AutoOpen) {
            Start-Process "explorer.exe" "$DriveLetter`:\"
        }
        
        if ($CreateShortcut) {
            New-DesktopShortcut -DriveLetter $DriveLetter
        }
        
        if (-not $Silent) {
            # Интерактивные опции
            $choice = Read-Host "Открыть диск $DriveLetter`: в Проводнике? (y/n)"
            if ($choice -eq 'y' -or $choice -eq 'Y') {
                Start-Process "explorer.exe" "$DriveLetter`:\"
            }
            
            $shortcutChoice = Read-Host "Создать ярлык на рабочем столе? (y/n)"
            if ($shortcutChoice -eq 'y' -or $shortcutChoice -eq 'Y') {
                New-DesktopShortcut -DriveLetter $DriveLetter
            }
            
            Write-Host ""
            Write-Warning "Для остановки нажмите Ctrl+C или закройте окно"
            Write-Info "При закрытии диск будет отмонтирован автоматически"
            Write-Host ""
            
            # Фоновый мониторинг
            Write-Info "Работа в фоновом режиме..."
            try {
                while ($true) {
                    Start-Sleep -Seconds 60
                    $timestamp = Get-Date -Format "HH:mm:ss"
                    Write-Host "⏰ $timestamp - Диск $DriveLetter`: работает" -ForegroundColor Gray
                }
            } catch {
                # Пользователь прервал выполнение
            }
        }
        
        return 0
        
    } catch {
        Write-Error "Критическая ошибка: $_"
        return 1
    } finally {
        # Очистка при завершении
        if (-not $Silent) {
            Dismount-TelegramDrive
        }
    }
}

# Обработка Ctrl+C
$null = Register-EngineEvent PowerShell.Exiting -Action {
    Dismount-TelegramDrive
}

# Запуск
$exitCode = Main
exit $exitCode 