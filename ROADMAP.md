# 🛣️ ROADMAP - Техническое задание

## Цели расширения функциональности

Превратить базовый WebDAV-шлюз в полноценную систему управления Telegram как облачным диском с автоматизацией, GUI и защищённым доступом.

---

## 1. 🔧 rclone конфиг с встроенным WebDAV

### Цель
Автоматически генерировать и применять конфигурацию rclone без ручного ввода команд.

### Требования
- [x] ~~Скрипт `rclone_setup.py` создаёт remote "telegramdav"~~ ✅ Готово
- [ ] **Встроенная генерация конфига** внутри Python-приложения
- [ ] **Автоматическое обнаружение rclone** и установка при отсутствии
- [ ] **Валидация подключения** с retry-логикой
- [ ] **Шаблоны конфигураций** для разных use-case (backup, sync, mount)

### Техническая реализация
```python
# rclone_manager.py
class RcloneManager:
    def __init__(self, webdav_url="http://localhost:8080"):
        self.webdav_url = webdav_url
        
    def ensure_rclone_installed(self) -> bool:
        # Проверка/установка rclone
        
    def create_config(self, remote_name="telegramdav") -> bool:
        # Программная генерация ~/.config/rclone/rclone.conf
        
    def test_connection(self) -> bool:
        # Тест `rclone lsd remote:`
        
    def get_templates(self) -> Dict[str, str]:
        # Возвращает готовые команды sync/mount/copy
```

### Критерии готовности
- ✅ rclone автоматически настраивается одной командой
- ✅ Работает в Windows/Linux/macOS
- ✅ Создаёт файлы-шаблоны для типовых операций

---

## 2. 👁️ Watchdog-скрипт автозагрузки/синхронизации

### Цель
Мониторинг папок в реальном времени с автоматической загрузкой в Telegram при изменениях.

### Требования
- [ ] **Мониторинг файловой системы** (библиотека `watchdog`)
- [ ] **Правила синхронизации** (include/exclude паттерны)
- [ ] **Debouncing** (избежание множественных загрузок при быстрых изменениях)
- [ ] **Conflict resolution** (что делать с дубликатами)
- [ ] **Логирование операций** с ротацией логов
- [ ] **Restart-устойчивость** (восстановление после сбоев)

### Техническая реализация
```python
# watchdog_sync.py
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class TelegramSyncHandler(FileSystemEventHandler):
    def __init__(self, rclone_remote="telegramdav:/"):
        self.remote = rclone_remote
        self.debounce_timer = {}
        
    def on_modified(self, event):
        # Debounce + rclone sync
        
    def on_created(self, event):
        # Загрузка нового файла
        
    def on_deleted(self, event):
        # Опционально: удаление из Telegram

class SyncDaemon:
    def __init__(self, config_file="sync_rules.yaml"):
        # Загрузка правил синхронизации
        
    def start_monitoring(self, paths: List[str]):
        # Запуск Observer для каждого пути
        
    def stop_gracefully(self):
        # Graceful shutdown
```

### Конфигурация (`sync_rules.yaml`)
```yaml
sync_rules:
  - path: "C:/Users/<USER>/Documents"
    remote_path: "/documents"
    include: ["*.pdf", "*.docx", "*.txt"]
    exclude: ["temp/*", "*.tmp"]
    sync_mode: "upload_only"  # upload_only/bidirectional/download_only
    debounce_seconds: 5
    
  - path: "C:/Photos"
    remote_path: "/photos"
    include: ["*.jpg", "*.png", "*.mp4"]
    sync_mode: "bidirectional"
    conflict_resolution: "rename"  # rename/overwrite/skip
```

### Критерии готовности
- ✅ Отслеживает несколько папок одновременно
- ✅ Конфигурируется через YAML
- ✅ Логирует все операции
- ✅ Можно запустить как сервис/демон

---

## 3. 🔐 FastAPI WebDAV-обёртка с авторизацией

### Цель
Защищённый HTTP-интерфейс с аутентификацией, rate limiting и расширенными возможностями.

### Требования
- [ ] **HTTP Basic Auth / JWT токены**
- [ ] **Rate limiting** (защита от злоупотреблений)
- [ ] **CORS поддержка** (для веб-клиентов)
- [ ] **Расширенные WebDAV методы** (PROPFIND, MKCOL, DELETE)
- [ ] **Файловые метаданные** (размер, дата, тип)
- [ ] **Chunked upload** (загрузка больших файлов частями)
- [ ] **API документация** (Swagger/OpenAPI)

### Техническая реализация
```python
# webdav_api.py
from fastapi import FastAPI, Depends, HTTPException, UploadFile
from fastapi.security import HTTPBasic, HTTPBasicCredentials

app = FastAPI(title="Telegram WebDAV API", version="2.0")
security = HTTPBasic()

def authenticate(credentials: HTTPBasicCredentials = Depends(security)):
    # Проверка логина/пароля из config.env
    
@app.put("/webdav/{file_path:path}")
async def upload_file(file_path: str, file: UploadFile, user=Depends(authenticate)):
    # Загрузка в Telegram через существующий client
    
@app.get("/webdav/{file_path:path}")
async def download_file(file_path: str, user=Depends(authenticate)):
    # Скачивание из Telegram
    
@app.request("PROPFIND", "/webdav/{dir_path:path}")
async def list_directory(dir_path: str, user=Depends(authenticate)):
    # Листинг файлов (XML ответ по стандарту WebDAV)
    
@app.delete("/webdav/{file_path:path}")
async def delete_file(file_path: str, user=Depends(authenticate)):
    # Удаление сообщения из Telegram

# Middleware для rate limiting
@app.middleware("http")
async def rate_limit_middleware(request, call_next):
    # Ограничение requests/minute по IP
```

### Конфигурация (`auth_config.yaml`)
```yaml
auth:
  method: "basic"  # basic/jwt/none
  users:
    - username: "admin"
      password_hash: "$2b$12$..."
      permissions: ["read", "write", "delete"]
    - username: "readonly"
      password_hash: "$2b$12$..."
      permissions: ["read"]

rate_limiting:
  requests_per_minute: 60
  burst_limit: 10

webdav:
  enable_propfind: true
  enable_delete: false
  chunked_upload_size: "10MB"
```

### Критерии готовности
- ✅ Совместим со стандартными WebDAV клиентами
- ✅ Защищён авторизацией и rate limiting
- ✅ Документирован через Swagger UI
- ✅ Поддерживает все основные WebDAV операции

---

## 4. 🖥️ GUI-интерфейс управления Telegram-диском

### Цель
Пользовательский интерфейс для управления файлами, настройки синхронизации и мониторинга.

### Требования
- [ ] **Файловый менеджер** (просмотр, загрузка, удаление)
- [ ] **Настройка правил синхронизации** (GUI для sync_rules.yaml)
- [ ] **Мониторинг статуса** (connected/disconnected, последние операции)
- [ ] **Логи операций** с фильтрацией и поиском
- [ ] **Настройки конфигурации** (API ключи, пути, порты)
- [ ] **Системный трей** интеграция

### Технологический стек
**Вариант 1: Electron + React** (кроссплатформенный)
```typescript
// src/components/FileManager.tsx
export const FileManager = () => {
  const [files, setFiles] = useState([]);
  const [currentPath, setCurrentPath] = useState('/');
  
  const uploadFile = async (file: File) => {
    // Вызов API upload endpoint
  };
  
  const deleteFile = async (filePath: string) => {
    // Вызов API delete endpoint
  };
};
```

**Вариант 2: PyQt6/PySide6** (нативный Python)
```python
# gui/main_window.py
class TelegramDiskGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.api_client = TelegramWebDAVClient()
        
    def setup_ui(self):
        # File tree widget
        self.file_tree = QTreeWidget()
        # Upload/download buttons
        # Settings panel
        # Log viewer
        
    def refresh_files(self):
        # Получение списка файлов через API
        
    def upload_file(self):
        # Диалог выбора файла + загрузка
```

### Архитектура
```
GUI Application
├── File Manager (дерево файлов, операции)
├── Sync Rules Editor (настройка правил)
├── Status Monitor (состояние сервисов)
├── Settings Panel (конфигурация)
├── Log Viewer (просмотр логов)
└── System Tray (минимизация, быстрые действия)

Backend API (FastAPI)
├── /api/files (CRUD операции)
├── /api/sync-rules (управление правилами)
├── /api/status (состояние системы)
├── /api/logs (получение логов)
└── /api/config (настройки)
```

### Критерии готовности
- ✅ Интуитивный файловый менеджер
- ✅ Drag & Drop загрузка файлов
- ✅ Настройка через GUI (без редактирования файлов)
- ✅ Работает в системном трее
- ✅ Кроссплатформенность (Windows/Linux/macOS)

---

## 5. 📚 Обновление документации

### Новая структура README.md
- [ ] **Архитектурная диаграмма** (компоненты системы)
- [ ] **Сравнительная таблица** возможностей (Basic vs Full vs Enterprise)
- [ ] **Use cases** (сценарии использования)
- [ ] **API документация** (endpoints, примеры)
- [ ] **Troubleshooting** (частые проблемы и решения)

### Дополнительные документы
- [ ] **DEPLOYMENT.md** (Docker, systemd, Windows Service)
- [ ] **SECURITY.md** (рекомендации по безопасности)
- [ ] **CONTRIBUTING.md** (для разработчиков)
- [ ] **CHANGELOG.md** (история изменений)

---

## Приоритеты реализации

### Фаза 1 (MVP+) - 2 недели
1. ✅ Встроенный rclone-менеджер
2. ✅ Базовый watchdog для одной папки
3. ✅ FastAPI обёртка с Basic Auth

### Фаза 2 (Advanced) - 4 недели  
4. ✅ Полнофункциональный GUI (PyQt6)
5. ✅ Расширенные правила синхронизации
6. ✅ Документация и deployment guides

### Фаза 3 (Enterprise) - 6 недель
7. ✅ Микросервисная архитектура
8. ✅ Веб-интерфейс (React)
9. ✅ Метрики и мониторинг (Prometheus)

---

## Ожидаемый результат

**Полная экосистема для работы с Telegram как облачным диском:**
- 🔧 Автоматическая настройка (zero-config)
- 👁️ Реальтайм синхронизация файлов  
- 🔐 Защищённый API доступ
- 🖥️ Удобный GUI для всех операций
- 📚 Исчерпывающая документация

**Целевая аудитория:**
- Домашние пользователи (простая синхронизация)
- Разработчики (API интеграция)  
- Малый бизнес (корпоративное файлообменное решение) 