@echo off
chcp 65001 >nul
echo TelegramCloudBasic - Auto Mount
echo ========================================

cd /d "%~dp0"

REM Check virtual environment
if not exist "venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found!
    echo Please run setup.py first
    pause
    exit /b 1
)

REM Check configuration
if not exist "config.env" (
    echo ERROR: config.env not found!
    echo Please copy config_example.env to config.env and configure
    pause
    exit /b 1
)

REM Check rclone
if not exist "rclone.exe" (
    echo ERROR: rclone.exe not found!
    echo Please run: python install_rclone.py
    pause
    exit /b 1
)

REM Check WinFsp
if not exist "C:\Program Files\WinFsp\bin\winfsp-x64.dll" (
    if not exist "C:\Program Files (x86)\WinFsp\bin\winfsp-x64.dll" (
        echo ERROR: WinFsp not installed!
        echo Please run: python install_winfsp.py
        pause
        exit /b 1
    )
)

REM Find free drive letter
set DRIVE_LETTER=
for %%d in (T U V W X Y Z) do (
    if not exist "%%d:\" (
        set DRIVE_LETTER=%%d
        goto :found_drive
    )
)

echo ERROR: No free drive letters found!
pause
exit /b 1

:found_drive
echo Using drive %DRIVE_LETTER%:

REM Check if WebDAV is running
echo Checking WebDAV server...
netstat -ano | find "LISTENING" | find ":8080" >nul 2>&1
if %errorlevel%==0 (
    echo WebDAV already running
) else (
    echo Starting WebDAV server...
    start "TelegramWebDAV" /min venv\Scripts\python.exe telegram_webdav.py

    REM Wait for WebDAV to start
    echo Waiting for WebDAV to start...
    for /l %%i in (1,1,15) do (
        timeout /t 1 /nobreak >nul
        netstat -ano | find "LISTENING" | find ":8080" >nul 2>&1
        if not errorlevel 1 (
            echo WebDAV started
            goto :webdav_ready
        )
        echo    Attempt %%i/15...
    )

    echo ERROR: WebDAV failed to start in 15 seconds
    pause
    exit /b 1
)

:webdav_ready
echo.
echo Mounting drive %DRIVE_LETTER%:...
echo Telegram cloud will be available as drive %DRIVE_LETTER%:
echo WARNING: To disconnect, close this window or press Ctrl+C
echo.

REM Start mounting
"%~dp0rclone.exe" mount telegram: %DRIVE_LETTER%: ^
    --vfs-cache-mode writes ^
    --vfs-cache-max-size 1G ^
    --vfs-cache-max-age 1h ^
    --buffer-size 32M ^
    --dir-cache-time 5m ^
    --poll-interval 10s ^
    --timeout 30s ^
    --contimeout 10s ^
    --stats 30s ^
    --stats-one-line ^
    -v

echo.
echo Drive %DRIVE_LETTER%: unmounted
echo Auto-mount completed
pause
