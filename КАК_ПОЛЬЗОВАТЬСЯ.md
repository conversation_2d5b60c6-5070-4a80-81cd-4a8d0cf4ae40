# 🚀 Инструкция по использованию Telegram Cloud

У вас есть два способа работы с облаком. Выберите подходящий для вашей задачи.

---

## Способ 1: Обычный Диск (T:, Y:, Z:...)

**Для чего он?**
*   **Просмотр** файлов, которые уже в облаке.
*   **Скачивание** файлов из облака на компьютер.
*   Работа с **маленькими файлами** (документы, фото).

**Как запустить?**
*   Просто кликните на `START_TELEGRAM_CLOUD.bat`.

**‼️ ВАЖНО:**
При копировании **больших файлов** (фильмы, архивы) на этот диск, они сначала сохраняются во временную папку (кэш) на вашем диске `D:`. Загрузка в Telegram идет в фоне. Если в этот момент выключить компьютер или если закончится место на диске `D:`, **файл может быть потерян НАВСЕГДА.**

> **Вывод:** Удобно, но есть риск при загрузке больших файлов.

---

## Способ 2: Безопасная Загрузка

**Для чего он?**
*   Для **100% надежной загрузки больших и важных файлов** в облако.

**Как использовать?**
1.  Найдите скрипт `safe_upload.bat`.
2.  **Перетащите ваш файл** (например, `video.mp4`) прямо на иконку `safe_upload.bat`.
3.  Откроется окно, которое покажет прогресс загрузки. **Не закрывайте его!**
4.  Скрипт загрузит файл, проверит, что он не повредился, и **только после этого** переместит исходник с вашего компьютера в облако.

> **Вывод:** Максимальная надежность. Идеально для фильмов, бэкапов и всего, что нельзя терять.

---

### Кратко:

| Ваша задача                               | Какой способ использовать?        |
|--------------------------------------------|-----------------------------------|
| Посмотреть фильм в облаке                  | `START_TELEGRAM_CLOUD.bat`        |
| Скачать документ                           | `START_TELEGRAM_CLOUD.bat`        |
| **Загрузить новый фильм (5 ГБ)**             | `safe_upload.bat` (перетащить)    |
| **Сделать бэкап важных фото**             | `safe_upload.bat` (перетащить)    |
| Быстро закинуть текстовый файл             | `START_TELEGRAM_CLOUD.bat`        | 