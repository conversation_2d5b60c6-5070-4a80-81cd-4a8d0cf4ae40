@echo off
chcp 65001 >nul
title Telegram Cloud - СТАРТ
color 0A
echo.
echo 🚀 TELEGRAM CLOUD - ЗАПУСК 🚀
echo ==============================
echo.

REM Автопереход в директорию скрипта
cd /d "%~dp0"

setlocal EnableDelayedExpansion

REM Инициализация переменной для буквы диска
set "FREE_DRIVE="

REM Быстрая проверка
if not exist "telegram_webdav.py" (
    echo ❌ Основные файлы не найдены!
    pause & exit
)

if not exist "venv\Scripts\python.exe" (
    echo ❌ Виртуальное окружение не настроено!
    echo Запустите setup.py
    pause & exit
)

REM Активация окружения
call venv\Scripts\activate.bat

REM Поиск свободного диска
for %%d in (T G H X Y Z) do (
    if not exist "%%d:\" (
        set FREE_DRIVE=%%d
        goto found
    )
)
:found

if "%FREE_DRIVE%"=="" (
    echo ⚠️  Свободные буквы дисков не найдены, скрипт запустит только WebDAV.
    goto webdav_only
)

echo ✅ Найден свободный диск: %FREE_DRIVE%:
echo.

REM Запуск WebDAV в фоне
echo 🌐 Запуск WebDAV сервера...
start /min "WebDAV" venv\Scripts\python.exe telegram_webdav.py

REM Небольшая задержка
timeout /t 3 /nobreak >nul

echo.
echo 📡 Режим прямой загрузки: файлы сразу отправляются в Telegram
echo    Без локального кэширования для экономии места

REM Проверяем наличие rclone
set "RCLONE_PATH="
if exist "C:\Program Files\rclone\rclone.exe" set "RCLONE_PATH=C:\Program Files\rclone\rclone.exe"
if exist "rclone.exe" set "RCLONE_PATH=rclone.exe"
where rclone >nul 2>&1
if not errorlevel 1 set "RCLONE_PATH=rclone"

if "%RCLONE_PATH%"=="" (
    echo ⚠️  rclone не найден в системе. Диск будет недоступен, но WebDAV уже работает.
    echo    Установите rclone командой: python install_rclone.py
    goto finish
)

REM Попытка монтирования (если rclone есть)
set "LOG_FILE=rclone_mount.log"
echo 💾 Попытка монтирования диска %FREE_DRIVE%:...
echo    Используется: %RCLONE_PATH%
echo    📂 Кэш отключен - файлы сразу в облако
echo    📝 Лог rclone: %LOG_FILE%
start "TelegramMount" /min "%RCLONE_PATH%" mount telegram: %FREE_DRIVE%: --vfs-cache-mode off --transfers 1 --checkers 1 --buffer-size 0 --vfs-read-chunk-size 1M --vfs-read-chunk-size-limit 0 --dir-cache-time 1s --poll-interval 1s -vv --log-file "%LOG_FILE%"

goto after_mount

:webdav_only
 echo 🌐 Запуск WebDAV сервера без монтирования диска...
 start /min "WebDAV" venv\Scripts\python.exe telegram_webdav.py
 echo ✅ WebDAV запущен: http://localhost:8080
 goto finish

:after_mount

 REM Проверка результата
 timeout /t 5 /nobreak >nul
 if exist "%FREE_DRIVE%:\" (
     echo ✅ Диск %FREE_DRIVE%: готов!
 ) else (
     echo ⚠️  Диск не смонтирован.
 )
 
 if exist "%LOG_FILE%" (
     echo.
     echo 🔎 Последние строки журнала rclone:
     powershell -Command "Get-Content -Path '%LOG_FILE%' -Tail 20"
 )
 
 if exist "%FREE_DRIVE%:\" (
     echo.
     echo 📁 Откройте Проводник и найдите диск %FREE_DRIVE%:
     echo 🌐 WebDAV: http://localhost:8080
     explorer %FREE_DRIVE%:\
 ) else (
     echo 🌐 WebDAV продолжает работать: http://localhost:8080
 )

:finish
 echo.
 echo ✅ ГОТОВО! Не закрывайте это окно.
 pause 