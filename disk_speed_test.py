#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import random
import sys
from pathlib import Path

def test_disk_write_speed(file_size_mb=100, test_dir=".", chunk_size_mb=1):
    """
    Тестирует скорость записи на диск
    
    Args:
        file_size_mb: Размер тестового файла в МБ
        test_dir: Директория для теста
        chunk_size_mb: Размер блока записи в МБ
    """
    print(f"🔍 Тестирование скорости записи на диск...")
    print(f"📁 Директория: {os.path.abspath(test_dir)}")
    print(f"📦 Размер файла: {file_size_mb} МБ")
    print(f"🧱 Размер блока: {chunk_size_mb} МБ")
    print("-" * 50)
    
    test_file = os.path.join(test_dir, f"speed_test_{int(time.time())}.tmp")
    chunk_size = chunk_size_mb * 1024 * 1024  # В байтах
    total_size = file_size_mb * 1024 * 1024   # В байтах
    
    try:
        # Создаем тестовые данные
        test_data = bytearray(random.getrandbits(8) for _ in range(chunk_size))
        
        start_time = time.time()
        bytes_written = 0
        
        with open(test_file, 'wb') as f:
            while bytes_written < total_size:
                remaining = total_size - bytes_written
                write_size = min(chunk_size, remaining)
                
                if write_size < chunk_size:
                    # Обрезаем данные для последнего блока
                    f.write(test_data[:write_size])
                else:
                    f.write(test_data)
                
                bytes_written += write_size
                
                # Показываем прогресс
                progress = (bytes_written / total_size) * 100
                print(f"\r⏳ Прогресс: {progress:.1f}%", end="", flush=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ Запись завершена!")
        
        # Вычисляем скорости
        if duration > 0:
            speed_bps = total_size / duration
            speed_kbps = speed_bps / 1024
            speed_mbps = speed_kbps / 1024
        else:
            speed_bps = speed_kbps = speed_mbps = 0
        
        print(f"⏱ Время записи: {duration:.2f} секунд")
        print(f"🚀 Скорость записи: {speed_mbps:.2f} МБ/с")
        print(f"🚀 Скорость записи: {speed_kbps:.2f} КБ/с")
        
        return {
            "duration": duration,
            "speed_mbps": speed_mbps,
            "speed_kbps": speed_kbps,
            "file_size_mb": file_size_mb
        }
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        return None
        
    finally:
        # Удаляем тестовый файл
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🗑 Тестовый файл удален")

def test_disk_read_speed(file_size_mb=100, test_dir=".", chunk_size_mb=1):
    """
    Тестирует скорость чтения с диска
    """
    print(f"\n🔍 Тестирование скорости чтения с диска...")
    print(f"📁 Директория: {os.path.abspath(test_dir)}")
    print(f"📦 Размер файла: {file_size_mb} МБ")
    print("-" * 50)
    
    test_file = os.path.join(test_dir, f"speed_test_read_{int(time.time())}.tmp")
    chunk_size = chunk_size_mb * 1024 * 1024
    total_size = file_size_mb * 1024 * 1024
    
    try:
        # Сначала создаем файл для чтения
        print("📝 Создание тестового файла...")
        test_data = bytearray(random.getrandbits(8) for _ in range(chunk_size))
        
        with open(test_file, 'wb') as f:
            bytes_written = 0
            while bytes_written < total_size:
                remaining = total_size - bytes_written
                write_size = min(chunk_size, remaining)
                
                if write_size < chunk_size:
                    f.write(test_data[:write_size])
                else:
                    f.write(test_data)
                bytes_written += write_size
        
        print("📖 Начинаем чтение...")
        
        # Теперь читаем файл
        start_time = time.time()
        bytes_read = 0
        
        with open(test_file, 'rb') as f:
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                bytes_read += len(chunk)
                
                progress = (bytes_read / total_size) * 100
                print(f"\r⏳ Прогресс: {progress:.1f}%", end="", flush=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ Чтение завершено!")
        
        # Вычисляем скорости
        if duration > 0:
            speed_bps = total_size / duration
            speed_kbps = speed_bps / 1024
            speed_mbps = speed_kbps / 1024
        else:
            speed_bps = speed_kbps = speed_mbps = 0
        
        print(f"⏱ Время чтения: {duration:.2f} секунд")
        print(f"🚀 Скорость чтения: {speed_mbps:.2f} МБ/с")
        print(f"🚀 Скорость чтения: {speed_kbps:.2f} КБ/с")
        
        return {
            "duration": duration,
            "speed_mbps": speed_mbps,
            "speed_kbps": speed_kbps,
            "file_size_mb": file_size_mb
        }
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        return None
        
    finally:
        # Удаляем тестовый файл
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🗑 Тестовый файл удален")

def get_disk_info(path="."):
    """Получает информацию о диске"""
    try:
        import shutil
        total, used, free = shutil.disk_usage(path)
        
        print(f"\n💽 Информация о диске:")
        print(f"📁 Путь: {os.path.abspath(path)}")
        print(f"💾 Общий объем: {total / (1024**3):.2f} ГБ")
        print(f"📦 Использовано: {used / (1024**3):.2f} ГБ")
        print(f"💿 Свободно: {free / (1024**3):.2f} ГБ")
        print(f"📊 Использовано: {(used/total)*100:.1f}%")
        
    except Exception as e:
        print(f"❌ Не удалось получить информацию о диске: {e}")

def main():
    print("🎯 Тест скорости диска")
    print("=" * 50)
    
    # Параметры по умолчанию
    file_size = 50  # МБ
    test_directory = "."
    
    # Обработка аргументов командной строки
    if len(sys.argv) > 1:
        try:
            file_size = int(sys.argv[1])
        except ValueError:
            print("❌ Неверный размер файла. Используем 50 МБ по умолчанию.")
    
    if len(sys.argv) > 2:
        test_directory = sys.argv[2]
        if not os.path.exists(test_directory):
            print(f"❌ Директория {test_directory} не существует. Используем текущую.")
            test_directory = "."
    
    # Показываем информацию о диске
    get_disk_info(test_directory)
    
    # Тестируем скорость записи
    write_result = test_disk_write_speed(file_size, test_directory)
    
    # Тестируем скорость чтения
    read_result = test_disk_read_speed(file_size, test_directory)
    
    # Итоговый отчет
    print("\n" + "=" * 50)
    print("📋 ИТОГОВЫЙ ОТЧЕТ")
    print("=" * 50)
    
    if write_result:
        print(f"✍️  Скорость записи: {write_result['speed_mbps']:.2f} МБ/с")
    
    if read_result:
        print(f"📖 Скорость чтения: {read_result['speed_mbps']:.2f} МБ/с")
    
    print(f"📦 Размер тестового файла: {file_size} МБ")
    print(f"📁 Тестовая директория: {os.path.abspath(test_directory)}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Тест прерван пользователем")
    except Exception as e:
        print(f"\n❌ Ошибка: {e}") 