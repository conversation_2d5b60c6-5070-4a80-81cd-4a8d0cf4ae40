# 🛠️ Устранение неполадок

## ⚠️ Проблема: Выкинуло из Telegram-аккаунта

### Симптомы
- Telegram на телефоне/десктопе просит авторизоваться заново
- Сообщение "Session terminated" или "Unauthorized"
- WebDAV-шлюз работает, но основной Telegram не доступен

### Причина
WebDAV-шлюз создаёт дополнительную сессию через Telethon, что может конфликтовать с основными клиентами Telegram из-за ограничений на количество одновременных сессий.

---

## ✅ Порядок восстановления доступа

### 1. 📱 Восстановите основной Telegram

1. **Откройте Telegram** (телефон или десктоп)
2. **Введите номер телефона** вашего аккаунта
3. **Получите код подтверждения:**
   - 🔹 SMS на телефон
   - 🔹 Уведомление в другом активном клиенте
4. **Введите код** в Telegram

### 2. 🛡️ Очистите подозрительные сессии

После входа в Telegram:

1. Откройте **Настройки → Конфиденциальность и безопасность → Активные сессии**
2. Найдите сессии типа:
   - `Telethon Session`
   - `WebDAV Gateway`
   - `Unknown Device`
   - `Linux` (если WebDAV запущен на сервере)
3. **Завершите** все подозрительные сессии
4. При необходимости: **"Завершить все сессии кроме этой"**

### 3. 🔄 Перезапустите WebDAV-шлюз

```bash
# Остановите текущий процесс (Ctrl+C)
# Удалите старую сессию
rm webdav_session.session

# Перезапустите с новой авторизацией
python telegram_webdav.py
```

---

## 🔐 Рекомендации по безопасности

### ❌ НЕ РЕКОМЕНДУЕТСЯ
- Использовать основной Telegram-аккаунт для WebDAV
- Запускать несколько Telethon-сессий одновременно
- Игнорировать предупреждения о новых сессиях

### ✅ РЕКОМЕНДУЕТСЯ

#### Вариант 1: Отдельный аккаунт
1. Зарегистрируйте **новый номер телефона**
2. Создайте **отдельный Telegram-аккаунт** только для WebDAV
3. Используйте этот аккаунт в `config.env`

#### Вариант 2: Telegram Bot (ЛУЧШИЙ)
1. Создайте бота через [@BotFather](https://t.me/BotFather)
2. Получите `BOT_TOKEN`
3. Добавьте в `config.env`:
   ```env
   TG_BOT_TOKEN=123456789:AAABBBCCC...
   TG_API_HASH=  # оставьте пустым при использовании бота
   ```

---

## 🚨 Частые проблемы и решения

### Проблема: "The api_id/api_hash combination is invalid"
**Решение:**
```bash
# Проверьте config.env
cat config.env

# Убедитесь, что API_ID и API_HASH правильные
# Получите новые на https://my.telegram.org
```

### Проблема: "Session file is corrupted"
**Решение:**
```bash
# Удалите испорченную сессию
rm webdav_session.session*

# Перезапустите с новой авторизацией
python telegram_webdav.py
```

### Проблема: "FloodWaitError: A wait of X seconds is required"
**Решение:**
```bash
# Telegram ограничил частоту запросов
# Подождите указанное время и перезапустите
```

### Проблема: WebDAV сервер не отвечает
**Решение:**
```bash
# Проверьте, запущен ли процесс
netstat -an | grep 8080

# Проверьте логи
python telegram_webdav.py

# Убедитесь, что порт не занят
lsof -i :8080  # Linux/Mac
netstat -ano | findstr 8080  # Windows
```

---

## 🔍 Диагностика

### Проверка состояния сессии
```python
# diagnostic.py
import asyncio
from telethon import TelegramClient

async def check_session():
    client = TelegramClient("webdav_session", API_ID, API_HASH)
    await client.start()
    
    me = await client.get_me()
    print(f"Авторизован как: {me.first_name}")
    
    # Список активных сессий
    sessions = await client(GetAuthorizationsRequest())
    for session in sessions.authorizations:
        print(f"Сессия: {session.device_model} ({session.platform})")
    
    await client.disconnect()

# Запуск: python diagnostic.py
```

### Проверка подключения к Telegram
```bash
# Тест доступности серверов Telegram
ping 149.154.167.50

# Проверка DNS
nslookup web.telegram.org
```

---

## 📞 Экстренное восстановление

### Если код не приходит в SMS:
1. **Подождите 15-30 минут** (возможна временная блокировка)
2. **Попробуйте другой Telegram-клиент** (Web, Desktop, Mobile)
3. **Проверьте спам-фильтры** SMS
4. **Обратитесь к оператору** связи

### Если потерян доступ к номеру:
1. **Восстановите SIM-карту** у оператора связи
2. Telegram **НЕ ПРЕДОСТАВЛЯЕТ** альтернативных способов восстановления
3. При утере номера аккаунт будет недоступен

### Если аккаунт заблокирован:
1. **Прочитайте ToS Telegram** (Terms of Service)
2. **Обратитесь в поддержку**: [@SpamBot](https://t.me/SpamBot)
3. **Дождитесь рассмотрения** (может занять несколько дней)

---

## 🛡️ Профилактика проблем

### Регулярные проверки
- Мониторьте активные сессии в Telegram
- Проверяйте логи WebDAV-шлюза
- Делайте резервные копии важных данных

### Безопасная настройка
```env
# config.env - безопасная конфигурация
TG_BOT_TOKEN=ваш_бот_токен  # вместо user API
TG_DEST_CHAT=@приватный_канал  # вместо "me"
TG_DEVICE_MODEL=WebDAV Gateway v2
TG_SYSTEM_VERSION=Server 2024
```

### Мониторинг
```bash
# Автоматическая проверка каждые 5 минут
*/5 * * * * curl -f http://localhost:8080 || systemctl restart telegram-webdav
```

---

## 📋 Контрольный список

- [ ] Использую бот-токен вместо user API
- [ ] WebDAV работает на отдельном аккаунте/канале
- [ ] Регулярно проверяю активные сессии
- [ ] Настроил мониторинг доступности
- [ ] Сделал резервную копию config.env
- [ ] Документировал процедуру восстановления

---

**💡 Помните:** Безопасность важнее удобства. Лучше потратить время на правильную настройку бота, чем рисковать основным аккаунтом Telegram. 