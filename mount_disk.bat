@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo ========================================
echo TELEGRAM CLOUD DISK - Быстрое монтирование
echo ========================================

REM Проверяем WinFsp
if not exist "C:\Program Files\WinFsp\bin\winfsp-x64.dll" (
    if not exist "C:\Program Files (x86)\WinFsp\bin\winfsp-x64.dll" (
        echo [ERROR] WinFsp не установлен!
        echo Запустите: python install_winfsp.py
        pause
        exit /b 1
    )
)

REM Проверяем rclone
where rclone >nul 2>&1
if errorlevel 1 (
    if not exist "C:\Program Files\rclone\rclone.exe" (
        echo [ERROR] rclone не найден!
        echo Запустите: python install_rclone.py
        pause
        exit /b 1
    )
    set RCLONE_PATH="C:\Program Files\rclone\rclone.exe"
) else (
    set RCLONE_PATH=rclone
)

REM Активируем venv
if exist "venv\Scripts\activate.bat" (
    echo Активация виртуального окружения...
    call "venv\Scripts\activate.bat"
) else (
    echo [WARNING] venv не найден, используем системный Python
)

REM Запускаем монтирование
echo Запуск Telegram Disk Mounter...
python mount_telegram.py

pause 