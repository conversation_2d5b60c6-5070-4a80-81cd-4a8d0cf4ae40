#!/usr/bin/env python3
"""
quick_check.py - Быстрая проверка системы TelegramCloudBasic
"""

import os
import sys
from pathlib import Path

def check_system():
    """Быстрая проверка готовности системы"""
    print("🔍 Быстрая проверка TelegramCloudBasic...")
    
    issues = []
    
    # Проверка виртуального окружения
    if not (hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)):
        issues.append("⚠️ Виртуальное окружение не активировано")
    
    # Проверка config.env
    if not Path("config.env").exists():
        issues.append("❌ Файл config.env не найден")
    
    # Проверка основных файлов
    required_files = ["run.py", "telegram_webdav.py", "api/main.py", "bot/main.py"]
    for file in required_files:
        if not Path(file).exists():
            issues.append(f"❌ Файл {file} не найден")
    
    # Проверка папки db
    if not Path("db").exists():
        Path("db").mkdir()
        print("📁 Создана папка db/")
    
    # Проверка основных зависимостей
    try:
        import telethon, aiohttp, telegram, fastapi, uvicorn
        print("✅ Основные зависимости установлены")
    except ImportError as e:
        issues.append(f"❌ Отсутствует зависимость: {e}")
    
    if issues:
        print("\n🚨 Найдены проблемы:")
        for issue in issues:
            print(f"  {issue}")
        print("\n📝 Запустите: python test_components.py для подробной диагностики")
        return False
    else:
        print("✅ Система готова к работе!")
        print("\n🚀 Для запуска используйте:")
        print("  python run.py")
        return True

if __name__ == "__main__":
    success = check_system()
    sys.exit(0 if success else 1)
