"""
telegram_webdav.py
==================
Упрощённый WebDAV-шлюз к Telegram.

Позволяет загружать (PUT) и скачивать (GET) файлы через rclone или любой WebDAV-клиент.

!!! ВНИМАНИЕ !!!
Это демонстрационный пример, не рассчитанный на высокую нагрузку и полный набор
функций WebDAV. Отсутствуют: LIST, DELETE, MOVE, авторизация и кэш-слой.

Зависимости:
    pip install telethon aiohttp

Переменные окружения (или файл config.env):
    TG_API_ID      – integer, API ID, полученный на https://my.telegram.org
    TG_API_HASH    – string, API HASH
    TG_BOT_TOKEN   – (опционально) токен бота, если используете Bot API вход
    TG_DEST_CHAT   – username/id канала или 'me' (по умолчанию)

Запуск:
    export TG_API_ID=123456
    export TG_API_HASH="abcdef0123456789abcdef0123456789"
    python telegram_webdav.py
    # Сервер поднимется на http://localhost:8080

Тестирование:
    curl -T file.txt http://localhost:8080/file.txt   # загрузка
    curl -O http://localhost:8080/file.txt            # скачивание
"""

import asyncio
import os
from pathlib import Path
from aiohttp import web
from telethon import TelegramClient
from telethon.errors import rpcerrorlist
from dotenv import load_dotenv
import aiohttp

# Увеличиваем лимит размера запроса до 2GB для больших файлов
CLIENT_MAX_SIZE = 2 * 1024 * 1024 * 1024  # 2GB

# Глобальная переменная для клиента
tg_client = None
config = {}

def load_config():
    """Загружает конфигурацию из файла config.env"""
    print("Загрузка конфигурации из config.env")
    
    # Ищем config.env в текущей папке или создаем на основе примера
    if not os.path.exists("config.env"):
        if os.path.exists("config_example.env"):
            import shutil
            shutil.copy("config_example.env", "config.env")
            print("Создан config.env на основе config_example.env")
            print("Отредактируйте config.env и укажите ваши данные!")
            exit(1)
        else:
            print("Файл config.env не найден!")
            exit(1)
    
    load_dotenv("config.env")
    
    # Читаем настройки
    global config
    config = {
        'api_id': int(os.getenv('TG_API_ID', 0)),
        'api_hash': os.getenv('TG_API_HASH', ''),
        'bot_token': os.getenv('TG_BOT_TOKEN', ''),
        'phone': os.getenv('TG_PHONE', ''),
        'dest_chat': os.getenv('TG_DEST_CHAT', ''),
        'port': int(os.getenv('WEBDAV_PORT', 8080)),
        'max_file_size': int(os.getenv('MAX_FILE_SIZE', 50)) * 1024 * 1024  # MB в байты
    }
    
    # Проверка обязательных параметров
    if not config['api_id'] or not config['api_hash']:
        print("Ошибка: TG_API_ID и TG_API_HASH обязательны!")
        exit(1)
    
    if not config['dest_chat']:
        print("Ошибка: TG_DEST_CHAT обязателен!")
        exit(1)
    
    # Определяем режим работы
    use_bot = bool(config['bot_token'])
    
    print("Настройки:")
    print(f"   Режим: {'Бот' if use_bot else 'Пользователь'}")
    print(f"   Целевой чат: {config['dest_chat']}")
    print(f"   Порт: {config['port']}")
    
    if not use_bot and not config['phone']:
        print("Предупреждение: Для пользовательского режима рекомендуется указать TG_PHONE")

async def get_client():
    """Получает авторизованный клиент Telegram"""
    global tg_client
    
    if tg_client and tg_client.is_connected():
        return tg_client
    
    use_bot = bool(config['bot_token'])
    
    if use_bot:
        print("Инициализация клиента бота...")
        tg_client = TelegramClient('bot_session', config['api_id'], config['api_hash'])
        await tg_client.start(bot_token=config['bot_token'])
        me = await tg_client.get_me()
        print(f"Авторизация успешна: {me.username or me.first_name}")
    else:
        print("Инициализация пользовательского клиента...")
        print("ВНИМАНИЕ: Пользовательский API может быть небезопасен!")
        tg_client = TelegramClient('user_session', config['api_id'], config['api_hash'])
        await tg_client.start(phone=config['phone'])
        me = await tg_client.get_me()
        print(f"Авторизация успешна: {me.username or me.first_name}")
    
    return tg_client

async def handle_put(request: web.Request) -> web.Response:
    """Загрузка файла (HTTP PUT)."""
    try:
        tg_client = await get_client()
        path = request.path.lstrip("/")
        if not path:
            return web.Response(status=400, text="Path required")

        # Проверяем, что это не попытка создать директорию
        if path.endswith('/'):
            print(f"Пропускаю создание директории: {path}")
            return web.Response(status=201, text="Directory created")

        print(f"Загружаю файл: {path}")
        
        # Читаем данные файла
        data = await request.read()
        
        # Проверяем, что есть данные (не пустой файл/директория)
        if len(data) == 0:
            print(f"Пустые данные для {path}, возможно это директория")
            return web.Response(status=201, text="Empty file or directory")
        
        if len(data) > config['max_file_size']:
            print(f"Файл {path} слишком большой: {len(data)} байт (лимит: {config['max_file_size']} байт)")
            return web.Response(status=413, text="File too large")

        # Отправляем файл в Telegram
        await tg_client.send_file(
            config['dest_chat'], 
            data, 
            caption=path,
            force_document=True  # Принудительно как документ для больших файлов
        )
        
        print(f"Файл {path} успешно загружен ({len(data)} байт)")
        return web.Response(status=201, text="Created")
        
    except aiohttp.web_exceptions.HTTPRequestEntityTooLarge:
        print(f"Файл {path} превышает лимит aiohttp")
        return web.Response(status=413, text="Request entity too large")
    except Exception as e:
        print(f"Ошибка загрузки {path}: {e}")
        return web.Response(status=500, text=f"Upload error: {e}")

async def _find_message_by_caption(caption: str):
    """Ищет сообщение с файлом по caption"""
    tg_client = await get_client()
    try:
        # Для Bot API используем ограниченный поиск без search параметра
        if config['bot_token']:
            async for msg in tg_client.iter_messages(config['dest_chat'], limit=1000):
                if msg.file and msg.caption == caption:
                    return msg
        else:
            # Для User API можем использовать поиск
            async for msg in tg_client.iter_messages(config['dest_chat'], search=caption, limit=100):
                if msg.file and msg.caption == caption:
                    return msg
    except Exception as e:
        print(f"Ошибка поиска файла {caption}: {e}")
    return None

async def handle_get(request: web.Request) -> web.Response:
    """Скачивание файла (HTTP GET)."""
    try:
        tg_client = await get_client()
        path = request.path.lstrip("/")
        if not path:
            return web.Response(status=400, text="Path required")

        print(f"Поиск файла: {path}")
        msg = await _find_message_by_caption(path)
        if not msg:
            print(f"Файл {path} не найден")
            return web.Response(status=404, text="Not found")

        print(f"Скачиваю файл: {path}")
        data = await msg.download_media(bytes)
        
        print(f"Файл {path} скачан ({len(data)} байт)")
        return web.Response(
            body=data, 
            content_type='application/octet-stream'
        )
        
    except Exception as e:
        print(f"Ошибка скачивания {path}: {e}")
        return web.Response(status=500, text=f"Download error: {e}")

async def handle_options(request: web.Request) -> web.Response:
    """Обработка OPTIONS запросов (для CORS)."""
    print(f"OPTIONS-запрос для {request.path}")
    headers = {
        'Access-Control-Allow-Methods': 'GET, PUT, PROPFIND, MKCOL, DELETE, OPTIONS',
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type"
    }
    return web.Response(status=200, headers=headers)

async def handle_propfind(request: web.Request) -> web.Response:
    """Минимальная реализация PROPFIND для rclone.

    Поддерживаем только Depth: 0 или 1.
    Директории эмулируются по префиксу пути в caption сообщении.
    Возвращаем простой XML multistatus со списком файлов/директорий.
    """
    depth = request.headers.get('Depth', '1')
    path = request.path.lstrip('/')
    print(f"PROPFIND {path} (Depth={depth})")

    tg_client = await get_client()

    # Представляем root как пустую строку
    prefix = f"{path}/" if path else ''
    files = []
    dirs = set()

    try:
        # Для Bot API ограничены в поиске, берем последние сообщения
        if config['bot_token']:
            async for msg in tg_client.iter_messages(config['dest_chat'], limit=1000):
                if not msg.file or not msg.caption:
                    continue
                if not msg.caption.startswith(prefix):
                    continue
                rel = msg.caption[len(prefix):]
                if '/' in rel:
                    # Файл в поддиректории, добавляем первую папку
                    dir_name = rel.split('/', 1)[0]
                    dirs.add(dir_name)
                else:
                    files.append({
                        'name': rel,
                        'size': msg.file.size if msg.file else 0
                    })
        else:
            # Для User API используем поиск
            async for msg in tg_client.iter_messages(config['dest_chat'], search=prefix, limit=10000):
                if not msg.file or not msg.caption:
                    continue
                if not msg.caption.startswith(prefix):
                    continue
                rel = msg.caption[len(prefix):]
                if '/' in rel:
                    # Файл в поддиректории, добавляем первую папку
                    dir_name = rel.split('/', 1)[0]
                    dirs.add(dir_name)
                else:
                    files.append({
                        'name': rel,
                        'size': msg.file.size if msg.file else 0
                    })
    except Exception as e:
        print(f"Ошибка PROPFIND: {e}")

    # Составляем XML
    from xml.etree import ElementTree as ET

    NS = {'d': 'DAV:'}
    ET.register_namespace('', 'DAV:')

    multistatus = ET.Element('{DAV:}multistatus')

    def add_response(href: str, is_dir=False, size=0):
        resp = ET.SubElement(multistatus, '{DAV:}response')
        ET.SubElement(resp, '{DAV:}href').text = f"/{href}" if href else '/'
        propstat = ET.SubElement(resp, '{DAV:}propstat')
        prop = ET.SubElement(propstat, '{DAV:}prop')
        ET.SubElement(prop, '{DAV:}displayname').text = href if href else ''
        if is_dir:
            resourcetype = ET.SubElement(prop, '{DAV:}resourcetype')
            ET.SubElement(resourcetype, '{DAV:}collection')
        else:
            ET.SubElement(prop, '{DAV:}getcontentlength').text = str(size)
            ET.SubElement(prop, '{DAV:}resourcetype')  # пустой для файла
        ET.SubElement(propstat, '{DAV:}status').text = 'HTTP/1.1 200 OK'

    # Текущая директория
    add_response(path, is_dir=True)

    if depth != '0':
        for d in sorted(dirs):
            href = f"{prefix}{d}/" if prefix else f"{d}/"
            add_response(href.rstrip('/'), is_dir=True)
        for f in files:
            href = f"{prefix}{f['name']}" if prefix else f['name']
            add_response(href, is_dir=False, size=f['size'])

    xml_bytes = ET.tostring(multistatus, encoding='utf-8')
    headers = {
        'Content-Type': 'application/xml; charset="utf-8"',
        'DAV': '1, 2',
        'MS-Author-Via': 'DAV'
    }
    return web.Response(status=207, body=xml_bytes, headers=headers)

async def handle_mkcol(request: web.Request) -> web.Response:
    """Создаёт "директорию". Физически в Telegram ничего не нужно – директории виртуальны."""
    path = request.path.lstrip('/')
    print(f"MKCOL {path}")
    # Просто подтверждаем создание – директории виртуальные
    return web.Response(status=201, text='Created')

async def handle_delete(request: web.Request) -> web.Response:
    """Удаление файла или виртуальной директории."""
    path = request.path.lstrip('/')
    print(f"DELETE {path}")
    tg_client = await get_client()
    try:
        # Удаляем все сообщения с этим caption (файл)
        # или начинающиеся с path/ (директория)
        count = 0
        # Для Bot API ограничены в поиске
        if config['bot_token']:
            async for msg in tg_client.iter_messages(config['dest_chat'], limit=1000):
                if not msg.file or not msg.caption:
                    continue
                if msg.caption == path or msg.caption.startswith(f"{path}/"):
                    await msg.delete()
                    count += 1
        else:
            # Для User API используем поиск
            async for msg in tg_client.iter_messages(config['dest_chat'], search=path, limit=None):
                if not msg.file or not msg.caption:
                    continue
                if msg.caption == path or msg.caption.startswith(f"{path}/"):
                    await msg.delete()
                    count += 1
        if count == 0:
            return web.Response(status=404, text='Not found')
        return web.Response(status=204)
    except Exception as e:
        print(f"Ошибка DELETE {path}: {e}")
        return web.Response(status=500, text=str(e))

async def main():
    """Основной цикл запуска"""
    await get_client()  # Проверяем подключение при старте
    
    # Создаем приложение с увеличенным лимитом размера
    app = web.Application(client_max_size=CLIENT_MAX_SIZE)
    app.router.add_put('/{path:.*}', handle_put)
    app.router.add_get('/{path:.*}', handle_get)
    app.router.add_options('/{path:.*}', handle_options)
    app.router.add_route('PROPFIND', '/{path:.*}', handle_propfind)
    app.router.add_route('MKCOL', '/{path:.*}', handle_mkcol)
    app.router.add_delete('/{path:.*}', handle_delete)
    
    runner = web.AppRunner(app)
    await runner.setup()
    
    host = '0.0.0.0'
    port = config['port']
    site = web.TCPSite(runner, host, port)
    
    print(f"WebDAV-сервер запущен на http://{host}:{port}")
    await site.start()
    
    while True:
        await asyncio.sleep(3600)

if __name__ == '__main__':
    try:
        load_config()
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nСервер остановлен.")
    except Exception as e:
        print(f"Критическая ошибка: {e}")
        if "cannot import name" in str(e):
            print("\nПроверьте зависимости: pip install -r requirements.txt") 