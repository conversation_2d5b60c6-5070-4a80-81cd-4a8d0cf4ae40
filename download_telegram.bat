@echo off
REM Скрипт скачивания из Telegram облака

set RCLONE_PATH="rclone"
set REMOTE_NAME=telegram

if "%2"=="" (
    echo Использование: %0 ^<удаленный_файл^> ^<локальная_папка^>
    echo Пример: %0 "document.pdf" "C:\Downloads"
    pause
    exit /b 1
)

set REMOTE_FILE=%1
set LOCAL_DIR=%2

echo 📥 Скачивание %REMOTE_FILE% из Telegram облака в %LOCAL_DIR%...

%RCLONE_PATH% copy %REMOTE_NAME%:/%REMOTE_FILE% "%LOCAL_DIR%" --progress

if %ERRORLEVEL% equ 0 (
    echo ✅ Скачивание завершено
) else (
    echo ❌ Ошибка скачивания
    pause
)
