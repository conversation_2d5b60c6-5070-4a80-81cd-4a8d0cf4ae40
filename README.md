# TelegramCloudBasic

Комплексное решение для использования Telegram как облачного диска с WebDAV-интерфейсом и поддержкой rclone. Проект позволяет монтировать Telegram как обычный диск и синхронизировать файлы.

## ✅ Статус проекта

**Все ошибки исправлены!** Система протестирована и готова к работе.

📋 **Последние исправления:**
- Исправлены пути к базе данных
- Добавлена загрузка переменных окружения из config.env
- Исправлены проблемы с импортами
- Добавлена обработка ошибок
- Создан тестовый скрипт для проверки системы

## 🧪 Проверка системы

Перед использованием запустите проверку:
```bash
# Быстрая проверка
python quick_check.py

# Полная диагностика
python test_components.py
```

## 🚀 Быстрый старт

### Автоматическая установка
```bash
# Клонируйте репозиторий
git clone <url>
cd TelegramCloudBasic

# Запустите автоматическую установку
python setup.py
```

### Ручная установка
1. Установите Python 3.8 или новее
2. Создайте виртуальное окружение:
   ```bash
   python -m venv venv
   # Windows:
   venv\Scripts\activate
   # Linux/Mac:
   source venv/bin/activate
   ```
3. Установите зависимости:
   ```bash
   pip install -r requirements.txt
   ```

## ⚙️ Настройка

1. **Скопируйте файл конфигурации:**
   ```bash
   cp config_example.env config.env
   ```

2. **Получите API ключи Telegram:**
   - Перейдите на https://my.telegram.org
   - Создайте приложение и получите `API ID` и `API Hash`

3. **Отредактируйте `config.env`:**
   ```env
   TG_API_ID=ваш_api_id
   TG_API_HASH=ваш_api_hash
   TG_DEST_CHAT=me
   ```

## 🏃 Запуск

### Универсальный запуск всех компонентов:
```bash
python run.py
```

### Отдельные компоненты:
```bash
# Только WebDAV сервер (для rclone)
python run.py --webdav-only

# Только веб-интерфейс  
python run.py --web-only

# Только бот
python run.py --bot-only
```

### Альтернативные способы запуска:

**Windows (batch файлы):**
```cmd
start_webdav.bat    # WebDAV сервер
start_web.bat       # Веб-интерфейс
```

**Прямой запуск:**
```bash
python telegram_webdav.py          # WebDAV сервер
uvicorn api.main:app --reload       # API сервер
python bot/main.py                  # Telegram бот
```

## ⚠️ Важные ограничения и рекомендации

### Безопасность
- **НЕ используйте основной Telegram-аккаунт** для WebDAV - это может привести к конфликту сессий
- **Рекомендуется:** создать отдельного бота через [@BotFather](https://t.me/BotFather)
- **Автоматическая настройка бота:** `python bot_setup_guide.py`
- При проблемах с авторизацией см. [TROUBLESHOOTING.md](TROUBLESHOOTING.md)

### Функциональные ограничения  
- Простой поиск файлов (по caption сообщения)
- Отсутствует полноценная файловая структура (папки эмулируются тегами)
- Ограничения Telegram: файлы до 2 ГБ, может удалять неактивные файлы

### Производительность
- Линейный поиск файлов (медленно при большом количестве)
- Нет кэширования метаданных
- Ограничения API Telegram на частоту запросов

Подробный план улучшений см. в [ROADMAP.md](ROADMAP.md)

## Использование Telegram как облака через rclone + WebDAV

Ниже приведён детальный **гайд по полной автоматизации** использования **Telegram как облачного хранилища через rclone**, используя промежуточный **WebDAV-шлюз**, который пересылает файлы между вашим ПК и Telegram.

### 🧩 Схема архитектуры
```
[ПК / Сервер] ←→ [rclone] ←→ [WebDAV-шлюз] ←→ [Telegram API]
```

### ⚙️ Пошаговая настройка

1. **Установите rclone**
   ```bash
   curl https://rclone.org/install.sh | sudo bash
   ```
2. **Разверните WebDAV-шлюз к Telegram**
   В репозитории добавлен файл `telegram_webdav.py` – минимальный рабочий прототип (см. раздел ниже).
3. **Создайте конфиг rclone**
   ```bash
   rclone config
   # Новый remote → webdav → URL http://localhost:8080 → vendor other
   ```
4. **Проверьте соединение**
   ```bash
   rclone ls telegramdav:
   ```
5. **Автосинхронизация** (пример cron)
   ```bash
   0 * * * * rclone sync ~/Backup telegramdav:/backup
   ```
6. **Монтирование как диск**
   ```bash
   rclone mount telegramdav:/ ~/TelegramDrive --vfs-cache-mode writes &
   ```

## 💿 Монтирование как диска в Windows

**Наиболее удобный способ** - подключить Telegram облако как обычный диск в Windows!

### 🔧 Установка WinFsp

```bash
# Автоматическая установка WinFsp (требуется для монтирования дисков)
python install_winfsp.py
```

### 💾 Интерактивное монтирование

```bash
# Запустить интерактивную программу монтирования
python mount_telegram.py

# Или через batch-файл
mount_disk.bat
```

### ⚡ Быстрое монтирование

```bash
# Сразу подключить как диск T:
mount_telegram.bat T

# Подключить как диск G:
mount_telegram.bat G
```

После монтирования облако будет доступно в **Проводнике Windows** как обычный диск! Вы сможете:
- 📁 Копировать/вставлять файлы мышью
- 🗃️ Создавать папки и перемещать файлы
- 🔍 Искать файлы через поиск Windows
- 📷 Просматривать изображения в миниатюрах
- 🎬 Воспроизводить видео прямо с диска

## 🔧 Настройка rclone

Автоматическая настройка rclone для работы с Telegram:

```bash
python rclone_setup.py
```

Или ручная настройка:
```bash
rclone config
# Выберите: webdav → http://localhost:8080 → vendor: other
```

### 📋 Команды rclone

```bash
# Список файлов
rclone ls telegramdav:

# Загрузка файла
rclone copy file.txt telegramdav:/

# Скачивание файла  
rclone copy telegramdav:/file.txt ./

# Синхронизация папки
rclone sync ~/Backup telegramdav:/backup

# Монтирование как диск (Linux/Mac)
rclone mount telegramdav:/ ~/TelegramDrive --daemon
```

### 🤖 Автоматизация

**Linux/Mac (cron):**
```bash
# Синхронизация каждый час
0 * * * * rclone sync ~/Backup telegramdav:/backup
```

**Windows (Task Scheduler):**
```cmd
# Используйте созданные .bat файлы
sync_to_telegram.bat "C:\Backup"
```

## 🗺️ Планы развития

Проект будет расширяться поэтапно от базового WebDAV-шлюза до полноценной системы управления Telegram как облачным диском. Подробное ТЗ смотрите в [ROADMAP.md](ROADMAP.md).

### Текущий статус: MVP ✅
- [x] WebDAV-шлюз к Telegram (upload/download)
- [x] Автоустановка и настройка
- [x] rclone интеграция
- [x] Поддержка User API и Bot API

### Следующие этапы:
- [ ] **Watchdog синхронизация** - автозагрузка при изменении файлов
- [ ] **GUI интерфейс** - управление через графический интерфейс
- [ ] **FastAPI обёртка** - защищённый API с авторизацией
- [ ] **Расширенные возможности** - метаданные, chunked upload, PROPFIND

## 🔄 Автозапуск WebDAV-сервера в Windows 10/11

Чтобы WebDAV-шлюз запускался автоматически при входе в систему, используйте **любой** из готовых вариантов:

| Метод | Файл/команда | Особенности |
|-------|--------------|-------------|
| Папка автозагрузки | `autostart_simple.bat` | Самый простой. Создаёт `TelegramWebDAV.bat` в `%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup`. Сервер запустится в **видимом** окне. Удалить — просто удалить файл. |
| Планировщик задач *(рекомендуется)* | `python create_windows_task.py` | Создаёт задачу **TelegramWebDAV** (триггер — «Вход в систему»). Надёжно работает в фоновом режиме, окно не показывается. Управление: `taskschd.msc` или `schtasks`. |
| Интерактивный мастер | `python telegram_autostart.py` | Меню выбора: автозагрузка, скрытый VBS-запуск, запись в реестр, Windows Service. Можно так же удалить все методы автозапуска. |

### Проверка/управление задачей «TelegramWebDAV»
```powershell
# Запустить сразу
schtasks /run /tn TelegramWebDAV

# Остановить
schtasks /end /tn TelegramWebDAV

# Удалить полностью
schtasks /delete /tn TelegramWebDAV /f
```

После настройки автозапуска WebDAV-сервер поднимется автоматически, а диск, смонтированный через `rclone mount`, будет доступен без ручного запуска скриптов.

## 🔧 Исправленные ошибки

Подробный список исправленных ошибок см. в файле [ИСПРАВЛЕННЫЕ_ОШИБКИ.md](ИСПРАВЛЕННЫЕ_ОШИБКИ.md).

### Основные исправления:
- ✅ Исправлены пути к базе данных во всех модулях
- ✅ Добавлена загрузка переменных окружения из config.env
- ✅ Исправлены проблемы с импортами (speed_monitor, matplotlib)
- ✅ Улучшена обработка ошибок и проверка конфигурации
- ✅ Добавлены тестовые скрипты для диагностики
- ✅ Исправлены проблемы с типами данных и сравнениями

### Тестирование:
```bash
# Быстрая проверка готовности системы
python quick_check.py

# Полная диагностика всех компонентов
python test_components.py
```

---
