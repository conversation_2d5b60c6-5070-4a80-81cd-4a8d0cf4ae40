@echo off
chcp 65001 >nul
title Безопасная загрузка в Telegram

:: Переходим в папку со скриптом
cd /d "%~dp0"

setlocal EnableDelayedExpansion

REM === ПРОВЕРКА RCLONE ===
set "RCLONE_PATH="
if exist "C:\Program Files\rclone\rclone.exe" (
    set "RCLONE_PATH=C:\Program Files\rclone\rclone.exe"
) else if exist "rclone.exe" (
    set "RCLONE_PATH=.\rclone.exe"
) else (
    where rclone >nul 2>&1
    if !errorlevel! equ 0 set "RCLONE_PATH=rclone"
)

if "!RCLONE_PATH!"=="" (
    echo.
    echo  ❌ rclone не найден!
    echo     Запустите install_rclone.py или убедитесь, что rclone доступен в системе.
    echo.
    pause
    exit /b
)

REM === ПРОВЕРКА АРГУМЕНТА ===
if "%~1"=="" (
    echo.
    echo  ❌ Ошибка: Вы не указали файл для загрузки.
    echo.
    echo  КАК ИСПОЛЬЗОВАТЬ:
    echo  1. Перетащите файл прямо на этот скрипт (safe_upload.bat).
    echo     -ИЛИ-
    echo  2. Запустите из командной строки:
    echo     safe_upload.bat "C:\путь\к\файлу.mp4"
    echo.
    pause
    exit /b
)

set "FILE_TO_UPLOAD=%~1"
echo.
echo 🚀 БЕЗОПАСНАЯ ЗАГРУЗКА В TELEGRAM
echo ====================================
echo.
echo  Файл: %FILE_TO_UPLOAD%
echo.
echo  ⏳ Идет загрузка... Пожалуйста, не закрывайте это окно!
echo.

REM rclone move - загружает файл и удаляет локальную копию ТОЛЬКО при успехе.
REM --checksum   - проверяет, что файл на сервере идентичен исходному.
REM -P           - показывает прогресс.
"%RCLONE_PATH%" move "%FILE_TO_UPLOAD%" telegram:/ --checksum -P --no-traverse

if !errorlevel! equ 0 (
    echo.
    echo  ✅ УСПЕХ! Файл безопасно загружен в Telegram.
    echo     Исходный файл был перемещен.
) else (
    echo.
    echo  ❌ ОШИБКА! Не удалось загрузить файл.
    echo     Исходный файл НЕ был удален с вашего компьютера.
)

echo.
echo  Нажмите любую клавишу для выхода...
pause >nul 