# 📺 СОЗДАНИЕ КАНАЛА ДЛЯ TELEGRAM БОТА

## Быстрое решение проблемы Bot API

### Шаг 1: Создайте канал
1. Откройте Telegram
2. Нажмите ➕ → "Новый канал"
3. Название: `My Cloud Storage` (или любое)
4. Описание: `Хранилище файлов для Telegram Cloud`
5. Тип: **Приватный канал** (рекомендуется)

### Шаг 2: Добавьте бота
1. В настройках канала → "Администраторы"
2. "Добавить администратора"
3. Найдите: `@Mik345z674ziaBot`
4. Дайте права:
   - ✅ Отправка сообщений
   - ✅ Удаление сообщений
   - ✅ Редактирование сообщений

### Шаг 3: Получите ID канала
1. После добавления бота отправьте в канал команду: `/start`
2. Бот ответит с ID канала, либо:
3. Скопируйте ссылку на канал и используйте https://t.me/username_to_id_bot

### Шаг 4: Обновите config.env
```env
# Вариант 1: ID канала (если получили)
TG_DEST_CHAT=-1001234567890

# Вариант 2: Username канала (если есть)
TG_DEST_CHAT=@your_channel_name
```

## Альтернативное решение - Публичный канал

Если не хотите возиться с ID:

1. Создайте **публичный** канал
2. Задайте ему username (например: @my_cloud_files_123)
3. Добавьте бота как администратора
4. В config.env укажите: `TG_DEST_CHAT=@my_cloud_files_123`

## Готовый config.env пример:

```env
# Telegram API настройки
TG_API_ID=25936043
TG_API_HASH=080f4e94bb7e4bf8881e4163859f4fec

# Bot API (оставляем как есть)
TG_BOT_TOKEN=8082967180:AAEtUPfDyVjF9SF1oSrd-y5IMTX9KXAux20

# Целевой канал (ЗАМЕНИТЕ НА СВОЙ!)
TG_DEST_CHAT=@my_cloud_files_123

# Остальные настройки
WEBDAV_PORT=8080
WEB_PORT=8000
WEB_HOST=127.0.0.1
```

## Проверка работы
После настройки запустите: `START_TELEGRAM_CLOUD.bat`
Ошибки должны исчезнуть! 