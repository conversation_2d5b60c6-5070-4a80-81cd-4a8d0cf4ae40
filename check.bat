@echo off
chcp 65001 >nul
echo 🧪 Проверка TelegramCloudBasic
echo ========================================

cd /d "%~dp0"

if not exist "venv\Scripts\python.exe" (
    echo ❌ Виртуальное окружение не найдено!
    echo 📝 Сначала запустите setup.py для установки
    pause
    exit /b 1
)

echo 🔍 Быстрая проверка...
venv\Scripts\python.exe quick_check.py

echo.
echo 🧪 Полная диагностика...
venv\Scripts\python.exe test_components.py

echo.
echo ✅ Проверка завершена
pause
