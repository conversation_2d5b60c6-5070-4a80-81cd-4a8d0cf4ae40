#!/usr/bin/env python3
"""create_windows_task.py
Создаёт (или обновляет) задачу планировщика Windows, запускающую Telegram WebDAV
при входе пользователя в систему.
Работает на Windows 10/11. Требует права пользователя (админ не нужен).
"""
import os
import subprocess
import sys
from pathlib import Path

def create_task():
    proj_dir = Path(__file__).parent.resolve()
    python_exe = proj_dir / 'venv' / 'Scripts' / 'python.exe'
    webdav_script = proj_dir / 'telegram_webdav.py'

    if not python_exe.exists():
        print(f"[ERROR] Python venv not found: {python_exe}")
        print("Run setup.py first to create virtual environment.")
        sys.exit(1)

    if not webdav_script.exists():
        print(f"[ERROR] telegram_webdav.py not found: {webdav_script}")
        sys.exit(1)

    # Command that task scheduler will execute
    cmd = f'"{python_exe}" "{webdav_script}"'

    print("Creating/Updating scheduled task 'TelegramWebDAV' ...")

    # Delete existing task if present (ignore errors)
    subprocess.run(['schtasks', '/delete', '/tn', 'TelegramWebDAV', '/f'], capture_output=True, text=True)

    # Create new task (on logon, current user, run level limited)
    create_cmd = [
        'schtasks', '/create',
        '/tn', 'TelegramWebDAV',
        '/tr', cmd,
        '/sc', 'ONLOGON',
        '/rl', 'LIMITED',
        '/f'  # force overwrite
    ]

    result = subprocess.run(create_cmd, capture_output=True, text=True)
    if result.returncode == 0:
        print('[OK] Scheduled task created successfully!')
        print('    Task name : TelegramWebDAV')
        print('    Trigger   : At user logon')
        print('    Command   :', cmd)
    else:
        print('[ERROR] Failed to create task')
        print(result.stdout)
        print(result.stderr)
        sys.exit(result.returncode)

if __name__ == '__main__':
    if os.name != 'nt':
        print('This script works only on Windows.')
        sys.exit(1)
    create_task() 