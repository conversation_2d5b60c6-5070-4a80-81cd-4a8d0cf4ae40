#!/usr/bin/env python3
"""
mount_telegram.py - Монтирование Telegram как диска в Windows
Использует rclone mount + WinFsp
"""

import os
import sys
import subprocess
import time
import json
import threading
from pathlib import Path

class TelegramDiskMounter:
    def __init__(self):
        self.config_path = Path("config.env")
        self.rclone_config = Path.home() / ".config" / "rclone" / "rclone.conf"
        self.webdav_process = None
        self.mount_process = None
        self.available_drives = self.get_available_drives()
        
    def get_available_drives(self):
        """Получить список доступных букв дисков."""
        used_drives = set()
        for drive in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
            if Path(f"{drive}:\\").exists():
                used_drives.add(drive)
        
        # Предпочтительные буквы для облачных дисков
        preferred = ['T', 'C', 'D', 'G', 'H', 'X', 'Y', 'Z']
        available = []
        
        for drive in preferred:
            if drive not in used_drives:
                available.append(drive)
        
        # Добавляем остальные доступные
        for drive in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
            if drive not in used_drives and drive not in available:
                available.append(drive)
                
        return available[:5]  # Показываем первые 5 доступных
    
    def check_requirements(self):
        """Проверить требования для монтирования."""
        print("Проверка требований...")
        
        # 1. WinFsp
        winfsp_paths = [
            r"C:\Program Files (x86)\WinFsp\bin\winfsp-x64.dll",
            r"C:\Program Files\WinFsp\bin\winfsp-x64.dll"
        ]
        
        winfsp_found = any(Path(p).exists() for p in winfsp_paths)
        if not winfsp_found:
            print("WinFsp не найден")
            print("Запустите: python install_winfsp.py")
            return False
        print("WinFsp найден")
        
        # 2. rclone
        rclone_paths = [
            "rclone.exe",
            r"C:\Program Files\rclone\rclone.exe",
            Path("rclone.exe")
        ]
        
        rclone_path = None
        for path in rclone_paths:
            try:
                result = subprocess.run([str(path), "version"], 
                                      capture_output=True, timeout=5)
                if result.returncode == 0:
                    rclone_path = str(path)
                    break
            except:
                continue
                
        if not rclone_path:
            print("rclone не найден")
            print("Запустите: python install_rclone.py")
            return False
        print(f"rclone найден: {rclone_path}")
        self.rclone_path = rclone_path
        
        # 3. Конфигурация rclone
        if not self.rclone_config.exists():
            print("Конфигурация rclone не найдена")
            print("Запустите: python rclone_setup.py")
            return False
        print("Конфигурация rclone найдена")
        
        # 4. WebDAV сервер (telegram_webdav.py)
        webdav_script = Path("telegram_webdav.py")
        if not webdav_script.exists():
            print("telegram_webdav.py не найден")
            return False
        print("WebDAV сервер найден")
        
        return True
    
    def start_webdav_server(self):
        """Запустить WebDAV сервер в фоне."""
        print("Запуск WebDAV сервера...")
        
        try:
            # Запускаем в отдельном процессе
            self.webdav_process = subprocess.Popen([
                sys.executable, "telegram_webdav.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Ждем запуска сервера
            for i in range(10):
                try:
                    import requests
                    response = requests.get("http://localhost:8080", timeout=2)
                    print("WebDAV сервер запущен")
                    return True
                except:
                    time.sleep(1)
                    
            print("WebDAV сервер не запустился")
            return False
            
        except Exception as e:
            print(f"Ошибка запуска WebDAV: {e}")
            return False
    
    def mount_drive(self, drive_letter="T"):
        """Смонтировать диск."""
        print(f"Монтирование диска {drive_letter}:...")
        
        mount_point = f"{drive_letter}:"
        
        # Команда rclone mount
        mount_cmd = [
            self.rclone_path, "mount", "telegram:", mount_point,
            "--vfs-cache-mode", "writes",
            "--vfs-cache-max-size", "1G",
            "--vfs-cache-max-age", "1h",
            "--buffer-size", "32M",
            "--dir-cache-time", "5m",
            "--poll-interval", "10s",
            "--timeout", "30s",
            "--contimeout", "10s",
            "--low-level-retries", "3",
            "--retries", "3",
            "--stats", "30s",
            "--stats-one-line",
            "-v"
        ]
        
        print(f"Команда монтирования: {' '.join(mount_cmd)}")
        
        try:
            # Запускаем монтирование
            self.mount_process = subprocess.Popen(
                mount_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Мониторинг вывода
            def monitor_output():
                for line in iter(self.mount_process.stdout.readline, ''):
                    line = line.strip()
                    if line:
                        print(f"[rclone] {line}")
                        
                        # Проверяем успешное монтирование
                        if "The service rclone has been started" in line:
                            print(f"Диск {drive_letter}: успешно смонтирован!")
                        elif "ERROR" in line:
                            print(f"Ошибка монтирования: {line}")
            
            # Запускаем мониторинг в отдельном потоке
            monitor_thread = threading.Thread(target=monitor_output, daemon=True)
            monitor_thread.start()
            
            # Ждем немного и проверяем
            time.sleep(3)
            
            if self.mount_process.poll() is None:
                # Процесс еще работает - хорошо
                if Path(mount_point).exists():
                    print(f"Диск {drive_letter}: готов к использованию!")
                    print(f"Откройте Проводник -> Этот компьютер -> {drive_letter}:")
                    return True
                else:
                    print(f"Монтирование в процессе...")
                    return True
            else:
                print(f"Процесс монтирования завершился с кодом {self.mount_process.returncode}")
                return False
                
        except Exception as e:
            print(f"Ошибка монтирования: {e}")
            return False
    
    def unmount_drive(self, drive_letter="T"):
        """Отмонтировать диск."""
        print(f"Отмонтирование диска {drive_letter}:...")
        
        try:
            # Завершаем процесс монтирования
            if self.mount_process and self.mount_process.poll() is None:
                self.mount_process.terminate()
                self.mount_process.wait(timeout=10)
                print("Монтирование остановлено")
            
            # Альтернативный способ через taskkill
            subprocess.run([
                "taskkill", "/F", "/IM", "rclone.exe"
            ], capture_output=True)
            
        except Exception as e:
            print(f"Ошибка отмонтирования: {e}")
    
    def show_menu(self):
        """Показать главное меню."""
        print("\n" + "="*60)
        print("TELEGRAM CLOUD DISK - Монтирование как диска")
        print("="*60)
        print("1. Смонтировать диск")
        print("2. Отмонтировать диск") 
        print("3. Статус")
        print("4. Настройки")
        print("5. Выход")
        print("="*60)
    
    def show_drive_selection(self):
        """Показать выбор буквы диска."""
        print("\nВыберите букву диска:")
        for i, drive in enumerate(self.available_drives, 1):
            print(f"{i}. {drive}: (рекомендуется)")
        print("0. Ввести свою букву")
        
        while True:
            try:
                choice = input("\nВыбор (1-5): ").strip()
                if choice == "0":
                    custom = input("Введите букву диска (A-Z): ").strip().upper()
                    if len(custom) == 1 and custom.isalpha():
                        return custom
                    else:
                        print("Неверная буква диска")
                        continue
                        
                choice_num = int(choice)
                if 1 <= choice_num <= len(self.available_drives):
                    return self.available_drives[choice_num - 1]
                else:
                    print("Неверный выбор")
            except ValueError:
                print("Введите число")
    
    def show_status(self):
        """Показать статус системы."""
        print("\nСтатус системы:")
        print("-" * 40)
        
        # WebDAV сервер
        webdav_status = "Работает" if (self.webdav_process and 
                                         self.webdav_process.poll() is None) else "Остановлен"
        print(f"WebDAV сервер: {webdav_status}")
        
        # Монтирование
        mount_status = "Смонтирован" if (self.mount_process and 
                                           self.mount_process.poll() is None) else "Не смонтирован"
        print(f"Диск: {mount_status}")
        
        # Доступные диски
        print(f"Доступные буквы: {', '.join(self.available_drives)}")
        
        # Процессы rclone
        try:
            result = subprocess.run([
                "tasklist", "/FI", "IMAGENAME eq rclone.exe"
            ], capture_output=True, text=True)
            
            if "rclone.exe" in result.stdout:
                print("rclone процессы активны")
            else:
                print("rclone процессы не найдены")
        except:
            print("Не удалось проверить rclone процессы")
    
    def run(self):
        """Главный цикл программы."""
        print("Telegram Cloud Disk Mounter")
        print("Монтирование Telegram как диска в Windows")
        
        # Проверяем требования
        if not self.check_requirements():
            input("\nНажмите Enter для выхода...")
            return
        
        current_drive = None
        
        while True:
            self.show_menu()
            
            try:
                choice = input("\nВыбор (1-5): ").strip()
                
                if choice == "1":
                    # Монтирование
                    if self.mount_process and self.mount_process.poll() is None:
                        print("Диск уже смонтирован")
                        continue
                    
                    # Запускаем WebDAV если нужно
                    if not (self.webdav_process and self.webdav_process.poll() is None):
                        if not self.start_webdav_server():
                            continue
                    
                    # Выбираем букву диска
                    drive = self.show_drive_selection()
                    
                    # Монтируем
                    if self.mount_drive(drive):
                        current_drive = drive
                        input("\nДиск смонтирован! Нажмите Enter для продолжения...")
                    
                elif choice == "2":
                    # Отмонтирование
                    if current_drive:
                        self.unmount_drive(current_drive)
                        current_drive = None
                    else:
                        print("Нет смонтированных дисков")
                    
                elif choice == "3":
                    # Статус
                    self.show_status()
                    input("\nНажмите Enter для продолжения...")
                    
                elif choice == "4":
                    # Настройки
                    print("\nНастройки:")
                    print("1. Переконфигурировать rclone")
                    print("2. Переустановить WinFsp")
                    print("3. Очистить кеш")
                    
                    sub_choice = input("Выбор: ").strip()
                    if sub_choice == "1":
                        subprocess.run([sys.executable, "rclone_setup.py"])
                    elif sub_choice == "2":
                        subprocess.run([sys.executable, "install_winfsp.py"])
                    elif sub_choice == "3":
                        # Очистка кеша rclone
                        cache_dir = Path.home() / ".cache" / "rclone"
                        if cache_dir.exists():
                            import shutil
                            shutil.rmtree(cache_dir)
                            print("Кеш очищен")
                    
                elif choice == "5":
                    # Выход
                    print("Отмонтирование перед выходом...")
                    if current_drive:
                        self.unmount_drive(current_drive)
                    
                    if self.webdav_process:
                        self.webdav_process.terminate()
                    
                    print("До свидания!")
                    break
                    
                else:
                    print("Неверный выбор")
                    
            except KeyboardInterrupt:
                print("\n\nЗавершение работы...")
                if current_drive:
                    self.unmount_drive(current_drive)
                if self.webdav_process:
                    self.webdav_process.terminate()
                break
            except Exception as e:
                print(f"Ошибка: {e}")

def main():
    """Точка входа."""
    if os.name != 'nt':
        print("Этот скрипт работает только в Windows")
        return
    
    mounter = TelegramDiskMounter()
    mounter.run()

if __name__ == "__main__":
    main() 