# 🚀 Мониторинг скорости загрузки файлов

Этот документ описывает функционал измерения скорости загрузки файлов в TelegramCloudBasic.

## 📊 Возможности

### 1. Автоматическое измерение скорости в боте

Бот теперь автоматически измеряет и отображает:
- 📦 Размер файла
- ⏱ Время загрузки
- 🚀 Скорость загрузки (в МБ/с, КБ/с или Б/с)

### 2. Статистика скорости

Используйте команду `/stats` в боте для просмотра:
- Статистики за последние 24 часа
- Статистики за последние 7 дней
- Средней/максимальной скорости
- Общего объема загруженных файлов

### 3. API с расширенной информацией

API возвращает подробную информацию о скорости:
```json
{
  "id": 123,
  "file_name": "example.jpg",
  "upload_date": "2024-01-15 10:30:00",
  "file_size_mb": 5.25,
  "upload_duration_seconds": 2.1,
  "upload_speed_mbps": 2.5,
  "upload_speed_kbps": 2560
}
```

## 🛠 Утилиты

### SpeedMonitor класс

Класс для мониторинга и анализа скорости:

```python
from speed_monitor import SpeedMonitor

monitor = SpeedMonitor()

# Сохранение статистики
monitor.save_upload_stats("file.jpg", 5000000, 2.5, 2.0)

# Получение статистики
stats = monitor.get_speed_statistics(24)  # За 24 часа

# Создание графика
monitor.create_speed_chart(24, "speed_chart.png")
```

### Тест скорости диска

Для тестирования скорости записи/чтения диска:

```bash
# Тест с файлом 100 МБ в текущей директории
python disk_speed_test.py 100

# Тест с файлом 50 МБ в определенной директории
python disk_speed_test.py 50 /path/to/test/directory
```

## 📈 Команды бота

- `/start` - Запуск бота
- `/upload` - Загрузка файла (или просто отправьте файл)
- `/list` - Список загруженных файлов
- `/download <id>` - Скачивание файла по ID
- `/stats` - **НОВАЯ**: Статистика скорости загрузки

## 🔧 Установка зависимостей

Для работы графиков (опционально):
```bash
pip install matplotlib numpy
```

## 📁 Файлы данных

- `upload_stats.json` - История загрузок с метриками скорости
- `db/files.db` - База данных файлов
- `speed_chart.png` - График скорости (создается автоматически)

## 📊 Примеры вывода

### Бот при загрузке файла:
```
✅ Файл 'document.pdf' успешно загружен!
📋 ID файла: 123
📦 Размер: 15.25 МБ
⏱ Время загрузки: 8.5 сек
🚀 Скорость: 1.79 МБ/с
```

### Статистика в боте:
```
📊 Статистика скорости загрузки

За последние 24 часа:
• Загрузок: 15
• Средняя скорость: 2.34 МБ/с
• Максимальная скорость: 5.67 МБ/с
• Общий объем: 128.45 МБ

За последние 7 дней:
• Загрузок: 89
• Средняя скорость: 2.12 МБ/с
• Общий объем: 1024.78 МБ
```

### Тест скорости диска:
```
🎯 Тест скорости диска
==================================================

💽 Информация о диске:
📁 Путь: C:\Users\<USER>\Downloads
💾 Общий объем: 931.51 ГБ
📦 Использовано: 456.78 ГБ
💿 Свободно: 474.73 ГБ
📊 Использовано: 49.0%

🔍 Тестирование скорости записи на диск...
📁 Директория: C:\Users\<USER>\Downloads
📦 Размер файла: 100 МБ
🧱 Размер блока: 1 МБ
--------------------------------------------------
⏳ Прогресс: 100.0%
✅ Запись завершена!
⏱ Время записи: 1.23 секунд
🚀 Скорость записи: 81.30 МБ/с

==================================================
📋 ИТОГОВЫЙ ОТЧЕТ
==================================================
✍️  Скорость записи: 81.30 МБ/с
📖 Скорость чтения: 156.78 МБ/с
📦 Размер тестового файла: 100 МБ
```

## 🎯 Интеграция в ваш код

Чтобы добавить измерение скорости в любую функцию загрузки:

```python
import time
from speed_monitor import SpeedMonitor

def your_upload_function(file_path):
    monitor = SpeedMonitor()
    
    # Получаем размер файла
    file_size = os.path.getsize(file_path)
    
    # Начинаем измерение
    start_time = time.time()
    
    # Ваша логика загрузки файла
    upload_file_somewhere(file_path)
    
    # Заканчиваем измерение
    end_time = time.time()
    duration = end_time - start_time
    
    # Вычисляем скорость
    if duration > 0:
        speed_mbps = (file_size / duration) / (1024 * 1024)
    else:
        speed_mbps = 0
    
    # Сохраняем статистику
    monitor.save_upload_stats(
        os.path.basename(file_path),
        file_size,
        duration,
        speed_mbps
    )
    
    print(f"Скорость загрузки: {speed_mbps:.2f} МБ/с")
```

## 🚨 Важные заметки

1. **Точность измерений**: Скорость зависит от многих факторов (сеть, диск, размер файла)
2. **Малые файлы**: Для файлов менее 1 МБ измерения могут быть неточными
3. **Сетевые задержки**: При загрузке через Telegram API учитывается время сетевых запросов
4. **Дисковое пространство**: Убедитесь, что есть достаточно места для тестовых файлов

## 📝 Логирование

Все измерения автоматически логируются:
```
INFO - Файл загружен: document.pdf, размер: 15.25 МБ, время: 8.5с, скорость: 1.79 МБ/с
``` 