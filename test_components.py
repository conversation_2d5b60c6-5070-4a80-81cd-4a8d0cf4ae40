#!/usr/bin/env python3
"""
test_components.py - Тестирование компонентов TelegramCloudBasic
"""

import os
import sys
import importlib.util
from pathlib import Path

def test_import(module_name, file_path):
    """Тестирует импорт модуля"""
    print(f"🔍 Тестирование {module_name}...")
    try:
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print(f"✅ {module_name} - импорт успешен")
        return True
    except Exception as e:
        print(f"❌ {module_name} - ошибка импорта: {e}")
        return False

def test_config():
    """Тестирует конфигурацию"""
    print("🔍 Тестирование конфигурации...")
    
    config_file = Path("config.env")
    if not config_file.exists():
        print("❌ Файл config.env не найден")
        return False
    
    # Проверяем основные переменные
    required_vars = []
    optional_vars = []
    
    with open(config_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                if key in ['TG_API_ID', 'TG_API_HASH']:
                    required_vars.append((key, value))
                elif key in ['TG_BOT_TOKEN', 'TG_DEST_CHAT']:
                    optional_vars.append((key, value))
    
    print(f"📋 Найдено обязательных переменных: {len(required_vars)}")
    print(f"📋 Найдено опциональных переменных: {len(optional_vars)}")
    
    # Проверяем наличие токена бота
    has_bot_token = any(var[0] == 'TG_BOT_TOKEN' and var[1].strip() for var in optional_vars)
    has_dest_chat = any(var[0] == 'TG_DEST_CHAT' and var[1].strip() for var in optional_vars)
    
    if has_bot_token and has_dest_chat:
        print("✅ Конфигурация бота найдена")
    else:
        print("⚠️ Конфигурация бота неполная")
    
    print("✅ Конфигурация проверена")
    return True

def test_database():
    """Тестирует создание базы данных"""
    print("🔍 Тестирование базы данных...")
    
    db_dir = Path("db")
    if not db_dir.exists():
        db_dir.mkdir()
        print("📁 Создана папка db/")
    
    # Тестируем создание базы данных
    import sqlite3
    db_path = "db/test_files.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_name TEXT NOT NULL,
                file_id TEXT NOT NULL,
                upload_date TEXT NOT NULL
            )
        ''')
        conn.commit()
        conn.close()
        
        # Удаляем тестовую базу
        os.remove(db_path)
        print("✅ База данных - тест успешен")
        return True
    except Exception as e:
        print(f"❌ База данных - ошибка: {e}")
        return False

def test_dependencies():
    """Тестирует зависимости"""
    print("🔍 Тестирование зависимостей...")
    
    dependencies = [
        ('telethon', 'Telethon'),
        ('aiohttp', 'aiohttp'),
        ('telegram', 'python-telegram-bot'),
        ('fastapi', 'FastAPI'),
        ('uvicorn', 'Uvicorn'),
        ('dotenv', 'python-dotenv')
    ]
    
    success_count = 0
    for module_name, package_name in dependencies:
        try:
            __import__(module_name)
            print(f"✅ {package_name}")
            success_count += 1
        except ImportError:
            print(f"❌ {package_name} - не установлен")
    
    print(f"📊 Установлено зависимостей: {success_count}/{len(dependencies)}")
    return success_count == len(dependencies)

def main():
    """Главная функция тестирования"""
    print("🧪 TelegramCloudBasic - Тестирование компонентов")
    print("=" * 50)
    
    tests = [
        ("Зависимости", test_dependencies),
        ("Конфигурация", test_config),
        ("База данных", test_database),
    ]
    
    # Тестирование импортов
    import_tests = [
        ("run.py", "run.py"),
        ("telegram_webdav.py", "telegram_webdav.py"),
        ("speed_monitor.py", "speed_monitor.py"),
        ("api.main", "api/main.py"),
        ("bot.main", "bot/main.py"),
    ]
    
    success_count = 0
    total_tests = len(tests) + len(import_tests)
    
    # Основные тесты
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        if test_func():
            success_count += 1
    
    # Тесты импортов
    print(f"\n📋 Импорты модулей")
    for module_name, file_path in import_tests:
        if test_import(module_name, file_path):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Результат: {success_count}/{total_tests} тестов пройдено")
    
    if success_count == total_tests:
        print("🎉 Все тесты пройдены успешно!")
        print("🚀 Система готова к запуску")
    else:
        print("⚠️ Некоторые тесты не пройдены")
        print("📝 Проверьте ошибки выше и исправьте их")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
