#!/usr/bin/env python3
"""
rclone_setup.py - Настройка rclone для TelegramCloudBasic
"""

import os
import sys
import subprocess
from pathlib import Path
import tempfile

def find_rclone():
    """Найти путь к rclone."""
    # Проверяем стандартные места
    locations = [
        "rclone",  # В PATH
        "C:/Program Files/rclone/rclone.exe",
        str(Path.cwd() / "rclone.exe"),
        str(Path.home() / "bin" / "rclone.exe")
    ]
    
    for location in locations:
        try:
            result = subprocess.run([location, "version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ rclone найден: {location}")
                return location
        except:
            continue
    
    return None

def create_webdav_config(rclone_path):
    """Создать конфигурацию WebDAV для Telegram."""
    config_name = "telegram"
    webdav_url = "http://localhost:8080"
    
    print(f"🔧 Создание конфигурации '{config_name}'...")
    
    # Команды для создания конфигурации
    config_commands = [
        [rclone_path, "config", "create", config_name, "webdav"],
        [rclone_path, "config", "update", config_name, "url", webdav_url],
        [rclone_path, "config", "update", config_name, "vendor", "other"]
    ]
    
    try:
        for cmd in config_commands:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode != 0:
                print(f"❌ Ошибка выполнения команды: {' '.join(cmd)}")
                print(f"Вывод: {result.stdout}")
                print(f"Ошибка: {result.stderr}")
                return False
        
        print(f"✅ Конфигурация '{config_name}' создана")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка создания конфигурации: {e}")
        return False

def test_connection(rclone_path):
    """Тестирование подключения к WebDAV."""
    print("🧪 Тестирование подключения...")
    
    try:
        # Создаем тестовый файл
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Тест подключения rclone к Telegram WebDAV")
            test_file = f.name
        
        test_filename = "rclone_test.txt"
        
        # Тест загрузки
        print("📤 Тест загрузки файла...")
        upload_cmd = [rclone_path, "copyto", test_file, f"telegram:/{test_filename}"]
        result = subprocess.run(upload_cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            print(f"❌ Ошибка загрузки: {result.stderr}")
            return False
        
        print("✅ Файл успешно загружен")
        
        # Тест скачивания
        print("📥 Тест скачивания файла...")
        download_file = Path(test_file).parent / "downloaded_test.txt"
        download_cmd = [rclone_path, "copyto", f"telegram:/{test_filename}", str(download_file)]
        result = subprocess.run(download_cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            print(f"❌ Ошибка скачивания: {result.stderr}")
            return False
        
        # Проверяем содержимое
        if download_file.exists():
            with open(download_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "Тест подключения" in content:
                    print("✅ Файл успешно скачан и содержимое корректно")
                    
                    # Убираем тестовые файлы
                    os.unlink(test_file)
                    os.unlink(download_file)
                    
                    return True
        
        print("❌ Содержимое скачанного файла некорректно")
        return False
        
    except Exception as e:
        print(f"❌ Ошибка тестирования: {e}")
        return False

def create_sync_scripts(rclone_path):
    """Создать скрипты синхронизации."""
    print("📝 Создание скриптов синхронизации...")
    
    # Скрипт для Windows
    sync_script = f"""@echo off
REM Скрипт синхронизации с Telegram облаком
REM Использование: sync_telegram.bat <локальная_папка>

set RCLONE_PATH="{rclone_path}"
set REMOTE_NAME=telegram

if "%1"=="" (
    echo Использование: %0 ^<локальная_папка^>
    echo Пример: %0 "C:\\Users\\<USER>\\Documents\\MyCloud"
    pause
    exit /b 1
)

set LOCAL_PATH=%1

echo 🔄 Синхронизация %LOCAL_PATH% с Telegram облаком...

REM Синхронизация (загрузка изменений)
%RCLONE_PATH% sync "%LOCAL_PATH%" %REMOTE_NAME%:/ --progress --transfers 1 --checkers 1

if %ERRORLEVEL% equ 0 (
    echo ✅ Синхронизация завершена успешно
) else (
    echo ❌ Ошибка синхронизации
    pause
)
"""
    
    with open("sync_telegram.bat", 'w', encoding='utf-8') as f:
        f.write(sync_script)
    
    # Скрипт загрузки
    upload_script = f"""@echo off
REM Скрипт загрузки файла в Telegram облако

set RCLONE_PATH="{rclone_path}"
set REMOTE_NAME=telegram

if "%1"=="" (
    echo Использование: %0 ^<файл_или_папка^>
    echo Пример: %0 "document.pdf"
    pause
    exit /b 1
)

set SOURCE=%1

echo 📤 Загрузка %SOURCE% в Telegram облако...

%RCLONE_PATH% copy "%SOURCE%" %REMOTE_NAME%:/ --progress

if %ERRORLEVEL% equ 0 (
    echo ✅ Загрузка завершена
) else (
    echo ❌ Ошибка загрузки
    pause
)
"""
    
    with open("upload_telegram.bat", 'w', encoding='utf-8') as f:
        f.write(upload_script)
    
    # Скрипт скачивания
    download_script = f"""@echo off
REM Скрипт скачивания из Telegram облака

set RCLONE_PATH="{rclone_path}"
set REMOTE_NAME=telegram

if "%2"=="" (
    echo Использование: %0 ^<удаленный_файл^> ^<локальная_папка^>
    echo Пример: %0 "document.pdf" "C:\\Downloads"
    pause
    exit /b 1
)

set REMOTE_FILE=%1
set LOCAL_DIR=%2

echo 📥 Скачивание %REMOTE_FILE% из Telegram облака в %LOCAL_DIR%...

%RCLONE_PATH% copy %REMOTE_NAME%:/%REMOTE_FILE% "%LOCAL_DIR%" --progress

if %ERRORLEVEL% equ 0 (
    echo ✅ Скачивание завершено
) else (
    echo ❌ Ошибка скачивания
    pause
)
"""
    
    with open("download_telegram.bat", 'w', encoding='utf-8') as f:
        f.write(download_script)
    
    print("✅ Созданы скрипты:")
    print("   📄 sync_telegram.bat - Синхронизация папки")
    print("   📤 upload_telegram.bat - Загрузка файлов")
    print("   📥 download_telegram.bat - Скачивание файлов")

def create_mount_script(rclone_path):
    """Создать скрипт для монтирования как диска."""
    print("📝 Создание скрипта монтирования диска...")
    
    mount_script = f"""@echo off
chcp 65001 >nul
REM Скрипт монтирования Telegram облака как диска

set RCLONE_PATH="{rclone_path}"
set REMOTE_NAME=telegram
set DEFAULT_DRIVE=T:

if "%1"=="" (
    set DRIVE_LETTER=%DEFAULT_DRIVE%
) else (
    set DRIVE_LETTER=%1:
)

echo 💿 Монтирование Telegram облака как диска %DRIVE_LETTER%...

REM Проверяем WinFsp
if not exist "C:\\Program Files\\WinFsp\\bin\\winfsp-x64.dll" (
    if not exist "C:\\Program Files (x86)\\WinFsp\\bin\\winfsp-x64.dll" (
        echo ❌ WinFsp не установлен!
        echo Скачайте с https://winfsp.dev/rel/
        pause
        exit /b 1
    )
)

echo 🚀 Запуск монтирования...
echo ⚠️  Для остановки нажмите Ctrl+C

%RCLONE_PATH% mount %REMOTE_NAME%: %DRIVE_LETTER% ^
    --vfs-cache-mode writes ^
    --vfs-cache-max-size 1G ^
    --vfs-cache-max-age 1h ^
    --buffer-size 32M ^
    --dir-cache-time 5m ^
    --poll-interval 10s ^
    --timeout 30s ^
    --contimeout 10s ^
    --stats 30s ^
    --stats-one-line ^
    -v

echo 📤 Диск отмонтирован
pause
"""
    
    with open("mount_telegram.bat", 'w', encoding='utf-8') as f:
        f.write(mount_script)
    
    print("✅ Создан скрипт: mount_telegram.bat")

def show_usage_examples(rclone_path):
    """Показать примеры использования."""
    print("\n🎯 Примеры использования:")
    print("=" * 50)
    
    print("📋 Основные команды rclone:")
    print(f'   {rclone_path} ls telegram:/                    # Список файлов')
    print(f'   {rclone_path} copy file.txt telegram:/         # Загрузить файл')
    print(f'   {rclone_path} copy telegram:/file.txt ./       # Скачать файл')
    print(f'   {rclone_path} sync ./MyFolder telegram:/       # Синхронизация папки')
    
    print("\n🛠️ Готовые скрипты:")
    print('   sync_telegram.bat "C:\\Users\\<USER>\\Documents"')
    print('   upload_telegram.bat "important.pdf"')
    print('   download_telegram.bat "document.pdf" "C:\\Downloads"')
    print('   mount_telegram.bat T                 # Монтировать как диск T:')
    print('   mount_disk.bat                      # Интерактивное монтирование')
    
    print("\n💡 Полезные команды:")
    print(f'   {rclone_path} mount telegram:/ T: --vfs-cache-mode writes')
    print("   ↑ Подключить как диск T: (требует WinFsp)")
    
    print(f'\n📊 Мониторинг:')
    print(f'   {rclone_path} size telegram:/                  # Размер всех файлов')
    print(f'   {rclone_path} about telegram:/                 # Информация о хранилище')

def main():
    print("🚀 Настройка rclone для TelegramCloudBasic")
    print("=" * 50)
    
    # Находим rclone
    rclone_path = find_rclone()
    if not rclone_path:
        print("❌ rclone не найден!")
        print("Установите rclone: python install_rclone.py")
        return False
    
    # Проверяем, что WebDAV сервер запущен
    print("🔍 Проверка WebDAV сервера...")
    try:
        import requests
        response = requests.get("http://localhost:8080", timeout=5)
        print("✅ WebDAV сервер доступен")
    except:
        print("❌ WebDAV сервер недоступен!")
        print("Запустите: python telegram_webdav.py")
        choice = input("Продолжить настройку? (y/N): ").strip().lower()
        if choice not in ['y', 'yes', 'д', 'да']:
            return False
    
    # Создаем конфигурацию
    if not create_webdav_config(rclone_path):
        return False
    
    # Тестируем подключение (если сервер доступен)
    try:
        import requests
        requests.get("http://localhost:8080", timeout=2)
        if not test_connection(rclone_path):
            print("⚠️ Тест подключения не прошел, но конфигурация создана")
    except:
        print("⚠️ Пропускаем тест (WebDAV сервер недоступен)")
    
    # Создаем скрипты
    create_sync_scripts(rclone_path)
    create_mount_script(rclone_path)
    
    # Показываем примеры
    show_usage_examples(rclone_path)
    
    print("\n🎉 Настройка rclone завершена!")
    return True

if __name__ == "__main__":
    if main():
        print("\n📋 Следующие шаги:")
        print("1. Убедитесь, что WebDAV сервер запущен: python telegram_webdav.py")
        print("2. Попробуйте: sync_telegram.bat \"C:\\Users\\<USER>\\Documents\"")
        print("3. Или используйте rclone напрямую")
    else:
        print("\n❌ Настройка не завершена")
        sys.exit(1) 