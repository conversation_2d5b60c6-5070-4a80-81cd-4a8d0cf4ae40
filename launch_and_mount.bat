@echo off
chcp 65001 >nul
title Telegram Cloud - Запуск и монтирование диска
color 0A

echo.
echo ████████╗███████╗██╗     ███████╗ ██████╗ ██████╗  █████╗ ███╗   ███╗
echo ╚══██╔══╝██╔════╝██║     ██╔════╝██╔════╝ ██╔══██╗██╔══██╗████╗ ████║
echo    ██║   █████╗  ██║     █████╗  ██║  ███╗██████╔╝███████║██╔████╔██║
echo    ██║   ██╔══╝  ██║     ██╔══╝  ██║   ██║██╔══██╗██╔══██║██║╚██╔╝██║
echo    ██║   ███████╗███████╗███████╗╚██████╔╝██║  ██║██║  ██║██║ ╚═╝ ██║
echo    ╚═╝   ╚══════╝╚══════╝╚══════╝ ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝     ╚═╝
echo.
echo                    🚀 ОБЛАЧНЫЙ ДИСК ТЕЛЕГРАМ 🚀
echo                     Автоматический запуск и монтирование
echo.
echo ═══════════════════════════════════════════════════════════════════════

set "PROJECT_DIR=%~dp0"
cd /d "%PROJECT_DIR%"

echo 📁 Рабочая директория: %PROJECT_DIR%
echo ⏰ Время запуска: %date% %time%
echo.

REM Проверка виртуального окружения
echo 🔍 Проверка виртуального окружения...
if not exist "venv\Scripts\activate.bat" (
    echo ❌ Виртуальное окружение не найдено!
    echo    Запустите setup.py для первоначальной настройки
    pause
    exit /b 1
)
echo ✅ Виртуальное окружение найдено

REM Активация виртуального окружения
echo 🔧 Активация виртуального окружения...
call "venv\Scripts\activate.bat"
if errorlevel 1 (
    echo ❌ Ошибка активации виртуального окружения!
    pause
    exit /b 1
)
echo ✅ Виртуальное окружение активировано

REM Проверка конфигурации
echo 🔍 Проверка конфигурации...
if not exist "config.env" (
    if exist "config_example.env" (
        echo 📝 Создание config.env из примера...
        copy "config_example.env" "config.env" >nul
        echo ⚠️  ВНИМАНИЕ: Отредактируйте config.env и укажите ваши данные!
        echo    После этого запустите скрипт заново
        start notepad "config.env"
        pause
        exit /b 1
    ) else (
        echo ❌ Файл конфигурации не найден!
        pause
        exit /b 1
    )
)
echo ✅ Конфигурация найдена

REM Проверка зависимостей
echo 🔍 Проверка основных компонентов...

REM Проверка telegram_webdav.py
if not exist "telegram_webdav.py" (
    echo ❌ telegram_webdav.py не найден!
    pause
    exit /b 1
)

REM Проверка WinFsp
set "WINFSP_FOUND=0"
if exist "C:\Program Files\WinFsp\bin\winfsp-x64.dll" set "WINFSP_FOUND=1"
if exist "C:\Program Files (x86)\WinFsp\bin\winfsp-x64.dll" set "WINFSP_FOUND=1"

if "%WINFSP_FOUND%"=="0" (
    echo ❌ WinFsp не установлен!
    echo    Запустите install_winfsp.py для установки
    set /p choice="Установить WinFsp автоматически? (y/n): "
    if /i "%choice%"=="y" (
        echo 📦 Установка WinFsp...
        python install_winfsp.py
        if errorlevel 1 (
            echo ❌ Ошибка установки WinFsp
            pause
            exit /b 1
        )
    ) else (
        pause
        exit /b 1
    )
)
echo ✅ WinFsp установлен

REM Проверка rclone
rclone version >nul 2>&1
if errorlevel 1 (
    echo ❌ rclone не найден!
    set /p choice="Установить rclone автоматически? (y/n): "
    if /i "%choice%"=="y" (
        echo 📦 Установка rclone...
        python install_rclone.py
        if errorlevel 1 (
            echo ❌ Ошибка установки rclone
            pause
            exit /b 1
        )
    ) else (
        pause
        exit /b 1
    )
)
echo ✅ rclone найден

REM Проверка конфигурации rclone
if not exist "%USERPROFILE%\.config\rclone\rclone.conf" (
    echo ❌ Конфигурация rclone не найдена!
    set /p choice="Настроить rclone автоматически? (y/n): "
    if /i "%choice%"=="y" (
        echo ⚙️  Настройка rclone...
        python rclone_setup.py
        if errorlevel 1 (
            echo ❌ Ошибка настройки rclone
            pause
            exit /b 1
        )
    ) else (
        pause
        exit /b 1
    )
)
echo ✅ Конфигурация rclone найдена

echo.
echo ═══════════════════════════════════════════════════════════════════════
echo                               🚀 ЗАПУСК
echo ═══════════════════════════════════════════════════════════════════════

REM Получение доступной буквы диска
echo 🔍 Поиск доступной буквы диска...
set "DRIVE_LETTER="
for %%d in (T G H X Y Z) do (
    if not exist "%%d:\" (
        set "DRIVE_LETTER=%%d"
        goto :found_drive
    )
)
:found_drive

if "%DRIVE_LETTER%"=="" (
    echo ❌ Нет доступных букв дисков!
    pause
    exit /b 1
)
echo ✅ Будет использован диск %DRIVE_LETTER%:

echo.
echo 🌐 Запуск WebDAV сервера...
start "Telegram WebDAV Server" /min cmd /c "python telegram_webdav.py"

REM Ждем запуска WebDAV сервера
echo ⏳ Ожидание запуска WebDAV сервера...
for /l %%i in (1,1,30) do (
    timeout /t 1 /nobreak >nul
    curl -s http://localhost:8080 >nul 2>&1
    if not errorlevel 1 (
        echo ✅ WebDAV сервер запущен
        goto :webdav_ready
    )
    echo    Попытка %%i/30...
)
echo ❌ WebDAV сервер не запустился за 30 секунд
pause
exit /b 1

:webdav_ready

echo.
echo 💾 Монтирование диска %DRIVE_LETTER%:...
start "Telegram Drive Mount" /min cmd /c "rclone mount telegram: %DRIVE_LETTER%: --vfs-cache-mode writes --vfs-cache-max-size 1G --dir-cache-time 5m --poll-interval 10s --timeout 30s --stats 30s -v"

REM Ждем монтирования диска
echo ⏳ Ожидание монтирования диска...
for /l %%i in (1,1,15) do (
    timeout /t 1 /nobreak >nul
    if exist "%DRIVE_LETTER%:\" (
        echo ✅ Диск %DRIVE_LETTER%: успешно смонтирован!
        goto :mount_ready
    )
    echo    Попытка %%i/15...
)
echo ⚠️  Диск может монтироваться в фоне...

:mount_ready

echo.
echo ═══════════════════════════════════════════════════════════════════════
echo                              🎉 ГОТОВО!
echo ═══════════════════════════════════════════════════════════════════════
echo.
echo ✅ Telegram облачный диск запущен и готов к работе!
echo.
echo 📁 Диск доступен по адресу: %DRIVE_LETTER%:\
echo 🌐 WebDAV сервер: http://localhost:8080
echo 💻 Откройте Проводник и перейдите в "Этот компьютер"
echo.
echo 🔗 Быстрый доступ:
echo    - Диск %DRIVE_LETTER%:      explorer %DRIVE_LETTER%:\
echo    - Веб-интерфейс: http://localhost:3000
echo.

REM Автоматическое открытие диска
set /p choice="Открыть диск %DRIVE_LETTER%: в Проводнике? (y/n): "
if /i "%choice%"=="y" (
    explorer %DRIVE_LETTER%:\
)

REM Создание ярлыка на рабочем столе
set /p shortcut="Создать ярлык на рабочем столе? (y/n): "
if /i "%shortcut%"=="y" (
    echo 🔗 Создание ярлыка...
    powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('$env:USERPROFILE\Desktop\Telegram Cloud %DRIVE_LETTER%.lnk'); $Shortcut.TargetPath = '%DRIVE_LETTER%:\'; $Shortcut.IconLocation = '%windir%\system32\imageres.dll,137'; $Shortcut.Description = 'Telegram облачный диск'; $Shortcut.Save()}"
    echo ✅ Ярлык создан на рабочем столе
)

echo.
echo ⚠️  Для остановки закройте это окно или нажмите Ctrl+C
echo    При закрытии диск будет отмонтирован автоматически
echo.
echo Нажмите любую клавишу для продолжения работы в фоне...
pause >nul

REM Переход в фоновый режим
echo 🔄 Работа в фоновом режиме...
echo Для полной остановки закройте это окно
echo.

:background_loop
timeout /t 60 /nobreak >nul
echo ⏰ %time% - Диск %DRIVE_LETTER%: работает
goto :background_loop 