#!/usr/bin/env python3
"""
one_click_mount.py - Запуск и монтирование Telegram диска одним кликом
==================================================================

Этот скрипт автоматически:
1. Проверяет все зависимости (WinFsp, rclone, конфигурации)
2. Устанавливает недостающие компоненты
3. Запускает WebDAV сервер
4. Монтирует диск в Windows
5. Предоставляет GUI для управления

Использование:
    python one_click_mount.py

Автор: TelegramCloudBasic
"""

import os
import sys
import time
import json
import subprocess
import threading
import webbrowser
from pathlib import Path
from typing import Optional, List
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import requests

class TelegramDiskLauncher:
    def __init__(self):
        self.project_dir = Path.cwd()
        self.config_path = self.project_dir / "config.env"
        self.rclone_config = Path.home() / ".config" / "rclone" / "rclone.conf"
        
        # Процессы
        self.webdav_process = None
        self.mount_process = None
        self.current_drive = None
        
        # GUI
        self.root = None
        self.status_var = None
        self.log_text = None
        self.drive_var = None
        
        # Доступные диски
        self.available_drives = self._get_available_drives()
        
    def _get_available_drives(self) -> List[str]:
        """Получить список доступных букв дисков."""
        used_drives = set()
        for drive in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
            if Path(f"{drive}:\\").exists():
                used_drives.add(drive)
        
        # Предпочтительные буквы
        preferred = ['T', 'G', 'H', 'X', 'Y', 'Z']
        available = []
        
        for drive in preferred:
            if drive not in used_drives:
                available.append(drive)
                
        return available[:5]
    
    def log(self, message: str, level: str = "INFO"):
        """Логирование сообщений."""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {level}: {message}"
        
        print(formatted_message)
        
        if self.log_text:
            self.log_text.insert(tk.END, formatted_message + "\n")
            self.log_text.see(tk.END)
            self.root.update_idletasks()
    
    def check_python_packages(self) -> bool:
        """Проверка Python пакетов."""
        self.log("Проверка Python пакетов...")
        
        required_packages = [
            'telethon', 'aiohttp', 'python-dotenv', 
            'requests', 'fastapi', 'uvicorn'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            self.log(f"Отсутствуют пакеты: {', '.join(missing_packages)}", "WARNING")
            
            if messagebox.askyesno("Установка пакетов", 
                                 f"Установить недостающие пакеты?\n{', '.join(missing_packages)}"):
                return self._install_packages(missing_packages)
            return False
        
        self.log("Все пакеты установлены")
        return True
    
    def _install_packages(self, packages: List[str]) -> bool:
        """Установка Python пакетов."""
        try:
            self.log("Установка пакетов...")
            
            # Активируем виртуальное окружение если есть
            if (self.project_dir / "venv" / "Scripts" / "activate.bat").exists():
                pip_path = self.project_dir / "venv" / "Scripts" / "pip.exe"
            else:
                pip_path = "pip"
            
            cmd = [str(pip_path), "install"] + packages
            
            process = subprocess.run(cmd, capture_output=True, text=True)
            
            if process.returncode == 0:
                self.log("Пакеты установлены успешно")
                return True
            else:
                self.log(f"Ошибка установки: {process.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Ошибка установки пакетов: {e}", "ERROR")
            return False
    
    def check_winfsp(self) -> bool:
        """Проверка WinFsp."""
        self.log("Проверка WinFsp...")
        
        winfsp_paths = [
            Path("C:/Program Files/WinFsp/bin/winfsp-x64.dll"),
            Path("C:/Program Files (x86)/WinFsp/bin/winfsp-x64.dll")
        ]
        
        if any(p.exists() for p in winfsp_paths):
            self.log("WinFsp найден")
            return True
        
        self.log("WinFsp не найден", "WARNING")
        
        if messagebox.askyesno("Установка WinFsp", 
                             "WinFsp не установлен. Установить автоматически?"):
            return self._install_winfsp()
        
        return False
    
    def _install_winfsp(self) -> bool:
        """Установка WinFsp."""
        try:
            self.log("Установка WinFsp...")
            
            if (self.project_dir / "install_winfsp.py").exists():
                result = subprocess.run([sys.executable, "install_winfsp.py"], 
                                      capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log("WinFsp установлен успешно")
                    return True
                else:
                    self.log(f"Ошибка установки WinFsp: {result.stderr}", "ERROR")
                    return False
            else:
                self.log("Скрипт install_winfsp.py не найден", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Ошибка установки WinFsp: {e}", "ERROR")
            return False
    
    def check_rclone(self) -> bool:
        """Проверка rclone."""
        self.log("Проверка rclone...")
        
        try:
            result = subprocess.run(["rclone", "version"], 
                                  capture_output=True, timeout=5)
            if result.returncode == 0:
                self.log("rclone найден")
                return True
        except:
            pass
        
        self.log("rclone не найден", "WARNING")
        
        if messagebox.askyesno("Установка rclone", 
                             "rclone не найден. Установить автоматически?"):
            return self._install_rclone()
        
        return False
    
    def _install_rclone(self) -> bool:
        """Установка rclone."""
        try:
            self.log("Установка rclone...")
            
            if (self.project_dir / "install_rclone.py").exists():
                result = subprocess.run([sys.executable, "install_rclone.py"], 
                                      capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log("rclone установлен успешно")
                    return True
                else:
                    self.log(f"Ошибка установки rclone: {result.stderr}", "ERROR")
                    return False
            else:
                self.log("Скрипт install_rclone.py не найден", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Ошибка установки rclone: {e}", "ERROR")
            return False
    
    def check_rclone_config(self) -> bool:
        """Проверка конфигурации rclone."""
        self.log("Проверка конфигурации rclone...")
        
        if self.rclone_config.exists():
            self.log("Конфигурация rclone найдена")
            return True
        
        self.log("Конфигурация rclone не найдена", "WARNING")
        
        if messagebox.askyesno("Настройка rclone", 
                             "Конфигурация rclone не найдена. Настроить автоматически?"):
            return self._setup_rclone()
        
        return False
    
    def _setup_rclone(self) -> bool:
        """Настройка rclone."""
        try:
            self.log("Настройка rclone...")
            
            if (self.project_dir / "rclone_setup.py").exists():
                result = subprocess.run([sys.executable, "rclone_setup.py"], 
                                      capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log("rclone настроен успешно")
                    return True
                else:
                    self.log(f"Ошибка настройки rclone: {result.stderr}", "ERROR")
                    return False
            else:
                self.log("Скрипт rclone_setup.py не найден", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Ошибка настройки rclone: {e}", "ERROR")
            return False
    
    def check_config(self) -> bool:
        """Проверка конфигурации проекта."""
        self.log("Проверка конфигурации...")
        
        if self.config_path.exists():
            self.log("Конфигурация найдена")
            return True
        
        # Создаем из примера
        example_config = self.project_dir / "config_example.env"
        if example_config.exists():
            self.log("Создание config.env из примера...")
            import shutil
            shutil.copy(example_config, self.config_path)
            
            messagebox.showinfo("Конфигурация", 
                              "Создан config.env из примера.\n"
                              "Отредактируйте файл и укажите ваши данные!")
            
            # Открываем файл для редактирования
            if sys.platform == "win32":
                os.startfile(self.config_path)
            else:
                subprocess.run(["xdg-open", self.config_path])
                
            return False
        
        self.log("Файл конфигурации не найден", "ERROR")
        return False
    
    def start_webdav_server(self) -> bool:
        """Запуск WebDAV сервера."""
        self.log("Запуск WebDAV сервера...")
        
        try:
            webdav_script = self.project_dir / "telegram_webdav.py"
            if not webdav_script.exists():
                self.log("telegram_webdav.py не найден", "ERROR")
                return False
            
            # Запускаем сервер
            self.webdav_process = subprocess.Popen([
                sys.executable, str(webdav_script)
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Ждем запуска
            for i in range(30):
                try:
                    response = requests.get("http://localhost:8080", timeout=2)
                    self.log("WebDAV сервер запущен")
                    return True
                except:
                    time.sleep(1)
                    if self.status_var:
                        self.status_var.set(f"Запуск WebDAV... {i+1}/30")
                        self.root.update_idletasks()
            
            self.log("WebDAV сервер не запустился", "ERROR")
            return False
            
        except Exception as e:
            self.log(f"Ошибка запуска WebDAV: {e}", "ERROR")
            return False
    
    def mount_drive(self, drive_letter: str) -> bool:
        """Монтирование диска."""
        self.log(f"Монтирование диска {drive_letter}:...")
        
        try:
            mount_cmd = [
                "rclone", "mount", "telegram:", f"{drive_letter}:",
                "--vfs-cache-mode", "writes",
                "--vfs-cache-max-size", "1G",
                "--vfs-cache-max-age", "1h",
                "--dir-cache-time", "5m",
                "--poll-interval", "10s",
                "--timeout", "30s",
                "--stats", "30s",
                "-v"
            ]
            
            self.mount_process = subprocess.Popen(
                mount_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # Ждем монтирования
            for i in range(15):
                if Path(f"{drive_letter}:\\").exists():
                    self.log(f"Диск {drive_letter}: успешно смонтирован!")
                    self.current_drive = drive_letter
                    return True
                
                time.sleep(1)
                if self.status_var:
                    self.status_var.set(f"Монтирование {drive_letter}:... {i+1}/15")
                    self.root.update_idletasks()
            
            self.log(f"Диск {drive_letter}: может монтироваться в фоне...", "WARNING")
            self.current_drive = drive_letter
            return True
            
        except Exception as e:
            self.log(f"Ошибка монтирования: {e}", "ERROR")
            return False
    
    def unmount_drive(self):
        """Размонтирование диска."""
        if self.mount_process:
            self.log("Размонтирование диска...")
            self.mount_process.terminate()
            self.mount_process = None
            
        if self.webdav_process:
            self.log("Остановка WebDAV сервера...")
            self.webdav_process.terminate()
            self.webdav_process = None
            
        self.current_drive = None
        self.log("Диск размонтирован")
    
    def create_gui(self):
        """Создание графического интерфейса."""
        self.root = tk.Tk()
        self.root.title("Telegram Cloud - Запуск и монтирование")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # Заголовок
        header_frame = ttk.Frame(self.root)
        header_frame.pack(fill="x", padx=10, pady=5)
        
        title_label = ttk.Label(header_frame, text="🚀 TELEGRAM ОБЛАЧНЫЙ ДИСК", 
                               font=("Arial", 16, "bold"))
        title_label.pack()
        
        subtitle_label = ttk.Label(header_frame, text="Автоматический запуск и монтирование")
        subtitle_label.pack()
        
        # Статус
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(status_frame, text="Статус:").pack(side="left")
        self.status_var = tk.StringVar(value="Готов к запуску")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(side="left", padx=(5, 0))
        
        # Выбор диска
        drive_frame = ttk.Frame(self.root)
        drive_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(drive_frame, text="Буква диска:").pack(side="left")
        self.drive_var = tk.StringVar(value=self.available_drives[0] if self.available_drives else "T")
        drive_combo = ttk.Combobox(drive_frame, textvariable=self.drive_var, 
                                  values=self.available_drives, width=5)
        drive_combo.pack(side="left", padx=(5, 0))
        
        # Кнопки управления
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        self.mount_button = ttk.Button(button_frame, text="🚀 Запустить и смонтировать", 
                                      command=self.mount_action)
        self.mount_button.pack(side="left", padx=(0, 5))
        
        self.unmount_button = ttk.Button(button_frame, text="⏹ Остановить", 
                                        command=self.unmount_action, state="disabled")
        self.unmount_button.pack(side="left", padx=(5, 0))
        
        self.open_button = ttk.Button(button_frame, text="📁 Открыть диск", 
                                     command=self.open_drive_action, state="disabled")
        self.open_button.pack(side="left", padx=(5, 0))
        
        # Лог
        log_frame = ttk.LabelFrame(self.root, text="Журнал")
        log_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.log_text = tk.Text(log_frame, height=15, wrap="word")
        log_scroll = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scroll.set)
        
        self.log_text.pack(side="left", fill="both", expand=True)
        log_scroll.pack(side="right", fill="y")
        
        # Меню
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Справка", menu=help_menu)
        help_menu.add_command(label="О программе", command=self.show_about)
        help_menu.add_command(label="Открыть веб-интерфейс", command=self.open_web_interface)
        
        # Обработка закрытия
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def mount_action(self):
        """Действие монтирования."""
        def mount_thread():
            try:
                self.mount_button.config(state="disabled")
                self.status_var.set("Проверка зависимостей...")
                
                # Проверяем все зависимости
                if not self.check_config():
                    self.status_var.set("Ошибка: Нет конфигурации")
                    return
                
                if not self.check_python_packages():
                    self.status_var.set("Ошибка: Проблемы с пакетами")
                    return
                
                if not self.check_winfsp():
                    self.status_var.set("Ошибка: WinFsp не установлен")
                    return
                
                if not self.check_rclone():
                    self.status_var.set("Ошибка: rclone не найден")
                    return
                
                if not self.check_rclone_config():
                    self.status_var.set("Ошибка: rclone не настроен")
                    return
                
                # Запускаем WebDAV
                if not self.start_webdav_server():
                    self.status_var.set("Ошибка: WebDAV не запустился")
                    return
                
                # Монтируем диск
                drive_letter = self.drive_var.get()
                if not self.mount_drive(drive_letter):
                    self.status_var.set("Ошибка: Диск не смонтирован")
                    return
                
                # Успех
                self.status_var.set(f"Диск {drive_letter}: готов к работе!")
                self.unmount_button.config(state="normal")
                self.open_button.config(state="normal")
                
                self.log("✅ Все готово! Диск можно использовать.")
                
            except Exception as e:
                self.log(f"Критическая ошибка: {e}", "ERROR")
                self.status_var.set("Критическая ошибка")
            finally:
                self.mount_button.config(state="normal")
        
        threading.Thread(target=mount_thread, daemon=True).start()
    
    def unmount_action(self):
        """Действие размонтирования."""
        self.unmount_drive()
        self.status_var.set("Остановлено")
        self.unmount_button.config(state="disabled")
        self.open_button.config(state="disabled")
    
    def open_drive_action(self):
        """Открытие диска в проводнике."""
        if self.current_drive:
            drive_path = f"{self.current_drive}:\\"
            if sys.platform == "win32":
                os.startfile(drive_path)
            else:
                subprocess.run(["xdg-open", drive_path])
    
    def show_about(self):
        """Показать информацию о программе."""
        messagebox.showinfo("О программе", 
                          "Telegram Cloud - Запуск и монтирование\n\n"
                          "Автоматический запуск WebDAV сервера\n"
                          "и монтирование Telegram как диска в Windows.\n\n"
                          "Версия: 1.0\n"
                          "Автор: TelegramCloudBasic")
    
    def open_web_interface(self):
        """Открыть веб-интерфейс."""
        webbrowser.open("http://localhost:8080")
    
    def on_closing(self):
        """Обработка закрытия окна."""
        if self.mount_process or self.webdav_process:
            if messagebox.askokcancel("Выход", "Размонтировать диск перед выходом?"):
                self.unmount_drive()
        self.root.destroy()
    
    def run(self):
        """Запуск приложения."""
        self.create_gui()
        self.log("Приложение запущено. Готов к работе!")
        self.root.mainloop()

def main():
    """Главная функция."""
    try:
        launcher = TelegramDiskLauncher()
        launcher.run()
    except Exception as e:
        print(f"Критическая ошибка: {e}")
        messagebox.showerror("Ошибка", f"Критическая ошибка: {e}")

if __name__ == "__main__":
    main() 