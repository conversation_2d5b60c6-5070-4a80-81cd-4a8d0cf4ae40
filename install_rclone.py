#!/usr/bin/env python3
"""
install_rclone.py - Автоматическая установка rclone для Windows
"""

import os
import sys
import zipfile
import requests
from pathlib import Path
import subprocess
import tempfile

def download_file(url, filename):
    """Скачать файл с прогресс-баром."""
    print(f"Скачиваю {filename}...")
    
    response = requests.get(url, stream=True)
    response.raise_for_status()
    
    total_size = int(response.headers.get('content-length', 0))
    downloaded = 0
    
    with open(filename, 'wb') as f:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                f.write(chunk)
                downloaded += len(chunk)
                if total_size > 0:
                    percent = (downloaded / total_size) * 100
                    print(f"\rПрогресс: {percent:.1f}%", end='', flush=True)
    
    print(f"\n✅ {filename} скачан")

def install_rclone_windows():
    """Установка rclone на Windows."""
    print("🔧 Установка rclone для Windows...")
    
    # Создаем временную папку
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # URL для скачивания rclone (Windows 64-bit)
        rclone_url = "https://downloads.rclone.org/rclone-current-windows-amd64.zip"
        zip_file = temp_path / "rclone.zip"
        
        try:
            # Скачиваем rclone
            download_file(rclone_url, zip_file)
            
            # Распаковываем
            print("📦 Распаковка rclone...")
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall(temp_path)
            
            # Находим папку с rclone
            rclone_dirs = [d for d in temp_path.iterdir() if d.is_dir() and d.name.startswith('rclone-')]
            if not rclone_dirs:
                raise Exception("Не найдена папка rclone в архиве")
            
            rclone_dir = rclone_dirs[0]
            rclone_exe = rclone_dir / "rclone.exe"
            
            if not rclone_exe.exists():
                raise Exception(f"rclone.exe не найден в {rclone_dir}")
            
            # Определяем куда установить
            install_options = [
                ("1", "Локально в проект", Path.cwd() / "rclone.exe"),
                ("2", "Системно (Program Files)", Path("C:/Program Files/rclone/rclone.exe")),
                ("3", "В PATH пользователя", Path.home() / "bin" / "rclone.exe")
            ]
            
            print("\n📍 Выберите способ установки:")
            for opt_id, desc, path in install_options:
                print(f"  {opt_id}. {desc}")
                print(f"     Путь: {path}")
            
            choice = input("\nВаш выбор (1-3): ").strip()
            
            if choice == "1":
                install_path = Path.cwd() / "rclone.exe"
                install_path.parent.mkdir(parents=True, exist_ok=True)
            elif choice == "2":
                install_path = Path("C:/Program Files/rclone/rclone.exe")
                install_path.parent.mkdir(parents=True, exist_ok=True)
            elif choice == "3":
                install_path = Path.home() / "bin" / "rclone.exe"
                install_path.parent.mkdir(parents=True, exist_ok=True)
            else:
                print("❌ Неверный выбор, устанавливаю локально")
                install_path = Path.cwd() / "rclone.exe"
            
            # Копируем rclone.exe
            print(f"📁 Копирование в {install_path}...")
            
            import shutil
            shutil.copy2(rclone_exe, install_path)
            
            print(f"✅ rclone установлен в {install_path}")
            
            # Проверяем установку
            try:
                if choice == "1":
                    result = subprocess.run([str(install_path), "version"], 
                                          capture_output=True, text=True, timeout=10)
                else:
                    result = subprocess.run(["rclone", "version"], 
                                          capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print("✅ rclone успешно установлен и работает!")
                    print(f"Версия: {result.stdout.split()[1] if len(result.stdout.split()) > 1 else 'unknown'}")
                    return str(install_path) if choice == "1" else "rclone"
                else:
                    print(f"⚠️ Установка завершена, но тест не прошел: {result.stderr}")
                    return str(install_path) if choice == "1" else "rclone"
                    
            except Exception as e:
                print(f"⚠️ Не удалось протестировать установку: {e}")
                return str(install_path) if choice == "1" else "rclone"
            
        except Exception as e:
            print(f"❌ Ошибка установки: {e}")
            return None

def check_rclone():
    """Проверить, установлен ли rclone."""
    try:
        result = subprocess.run(["rclone", "version"], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ rclone уже установлен в системе")
            return "rclone"
    except:
        pass
    
    # Проверяем локально
    local_rclone = Path.cwd() / "rclone.exe"
    if local_rclone.exists():
        try:
            result = subprocess.run([str(local_rclone), "version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ rclone найден локально")
                return str(local_rclone)
        except:
            pass
    
    return None

def main():
    print("🚀 Установка rclone")
    print("=" * 40)
    
    # Проверяем, нужна ли установка
    rclone_path = check_rclone()
    if rclone_path:
        print(f"rclone доступен по пути: {rclone_path}")
        choice = input("Переустановить? (y/N): ").strip().lower()
        if choice not in ['y', 'yes', 'д', 'да']:
            return rclone_path
    
    # Проверяем зависимости
    try:
        import requests
    except ImportError:
        print("📦 Устанавливаю зависимости...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
        import requests
    
    # Устанавливаем rclone
    if os.name == 'nt':  # Windows
        return install_rclone_windows()
    else:
        print("❌ Автоматическая установка поддерживается только для Windows")
        print("Установите rclone вручную: https://rclone.org/downloads/")
        return None

if __name__ == "__main__":
    rclone_path = main()
    if rclone_path:
        print(f"\n🎉 Установка завершена!")
        print(f"Путь к rclone: {rclone_path}")
        print("\n📋 Следующие шаги:")
        print("1. python rclone_setup.py  # Настройка конфигурации")
        print("2. Настройка синхронизации папок")
    else:
        print("\n❌ Установка не удалась")
        sys.exit(1) 