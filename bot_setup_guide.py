#!/usr/bin/env python3
"""
🤖 Гайд по безопасной настройке Telegram-бота для WebDAV

Этот скрипт поможет создать бота через BotFather и настроить конфигурацию
без риска для основного Telegram-аккаунта.
"""

import os
import re
from pathlib import Path

def print_header():
    print("=" * 60)
    print("🤖 Безопасная настройка Telegram-бота для WebDAV")
    print("=" * 60)
    print()

def check_botfather_steps():
    print("📋 Шаги создания бота через @BotFather:")
    print()
    print("1️⃣  Откройте Telegram и найдите @BotFather")
    print("2️⃣  Отправьте команду: /newbot")
    print("3️⃣  Введите имя бота (например: 'My WebDAV Bot')")
    print("4️⃣  Введите username бота (должен заканчиваться на 'bot')")
    print("5️⃣  Скопируйте полученный токен (выглядит как: 123456789:AAABBBCCC...)")
    print()
    
    token = input("🔑 Вставьте токен бота сюда: ").strip()
    
    # Проверка формата токена
    if not re.match(r'^\d+:[a-zA-Z0-9_-]+$', token):
        print("❌ Неверный формат токена!")
        print("   Токен должен выглядеть как: 123456789:AAABBBCCC...")
        return None
    
    print("✅ Токен принят!")
    return token

def setup_channel():
    print("\n📺 Настройка канала/чата для хранения:")
    print()
    print("Варианты:")
    print("1️⃣  Сохранённые сообщения: 'me' (простейший)")
    print("2️⃣  Приватный канал: создайте канал, добавьте бота как админа")
    print("3️⃣  Группа: создайте группу, добавьте бота")
    print()
    
    choice = input("Выберите вариант (1/2/3): ").strip()
    
    if choice == "1":
        return "me"
    elif choice == "2":
        channel = input("📺 Введите @username канала или ID: ").strip()
        print("\n⚠️  Не забудьте:")
        print("   • Добавить бота в канал как администратора")
        print("   • Дать права на отправку сообщений и управление файлами")
        return channel
    elif choice == "3":
        group = input("👥 Введите @username группы или ID: ").strip()
        print("\n⚠️  Не забудьте:")
        print("   • Добавить бота в группу")
        print("   • Дать права на отправку сообщений")
        return group
    else:
        print("❌ Неверный выбор, используем 'me'")
        return "me"

def update_config(bot_token, dest_chat):
    """Обновляет config.env с настройками бота"""
    config_path = Path("config.env")
    
    if config_path.exists():
        # Читаем существующий конфиг
        with open(config_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Обновляем нужные строки
        updated_lines = []
        bot_token_set = False
        dest_chat_set = False
        
        for line in lines:
            if line.startswith('TG_BOT_TOKEN='):
                updated_lines.append(f'TG_BOT_TOKEN={bot_token}\n')
                bot_token_set = True
            elif line.startswith('TG_DEST_CHAT='):
                updated_lines.append(f'TG_DEST_CHAT={dest_chat}\n')
                dest_chat_set = True
            elif line.startswith('TG_API_HASH='):
                # Очищаем API_HASH при использовании бота
                updated_lines.append('TG_API_HASH=\n')
            else:
                updated_lines.append(line)
        
        # Добавляем недостающие параметры
        if not bot_token_set:
            updated_lines.append(f'TG_BOT_TOKEN={bot_token}\n')
        if not dest_chat_set:
            updated_lines.append(f'TG_DEST_CHAT={dest_chat}\n')
    else:
        # Создаём новый конфиг для бота
        updated_lines = [
            f'TG_BOT_TOKEN={bot_token}\n',
            'TG_API_ID=\n',
            'TG_API_HASH=\n',
            f'TG_DEST_CHAT={dest_chat}\n',
            'TG_PORT=8080\n',
            'TG_DEVICE_MODEL=WebDAV Bot Gateway\n',
            'TG_SYSTEM_VERSION=Server 2024\n',
            'TG_APP_VERSION=1.0\n',
            'TG_LANG_CODE=ru\n'
        ]
    
    # Записываем обновлённый конфиг
    with open(config_path, 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)
    
    print(f"✅ Конфигурация сохранена в {config_path}")

def test_bot_connection(bot_token):
    """Тестирует подключение к боту"""
    print("\n🔍 Тестирование подключения к боту...")
    
    try:
        import asyncio
        import aiohttp
        
        async def check_bot():
            url = f"https://api.telegram.org/bot{bot_token}/getMe"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data['ok']:
                            bot_info = data['result']
                            print(f"✅ Бот работает!")
                            print(f"   Имя: {bot_info['first_name']}")
                            print(f"   Username: @{bot_info.get('username', 'N/A')}")
                            print(f"   ID: {bot_info['id']}")
                            return True
                    else:
                        print(f"❌ Ошибка API: {response.status}")
                        return False
        
        return asyncio.run(check_bot())
    
    except ImportError:
        print("⚠️  Пропускаем тест (нет aiohttp). Проверьте вручную:")
        print(f"   curl https://api.telegram.org/bot{bot_token}/getMe")
        return None
    except Exception as e:
        print(f"❌ Ошибка тестирования: {e}")
        return False

def show_next_steps():
    print("\n🚀 Что дальше:")
    print()
    print("1️⃣  Запустите WebDAV-шлюз:")
    print("    python telegram_webdav.py")
    print()
    print("2️⃣  Или используйте универсальный запуск:")
    print("    python run.py --webdav-only")
    print()
    print("3️⃣  Настройте rclone (если нужно):")
    print("    python rclone_setup.py")
    print()
    print("4️⃣  При проблемах смотрите:")
    print("    📖 TROUBLESHOOTING.md")
    print("    📋 ROADMAP.md")
    print()

def main():
    print_header()
    
    # Проверяем, есть ли уже конфиг с ботом
    config_path = Path("config.env")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'TG_BOT_TOKEN=' in content and any(line.startswith('TG_BOT_TOKEN=') and line.strip().split('=', 1)[1] for line in content.splitlines()):
                print("✅ Конфигурация бота уже существует!")
                choice = input("Хотите перенастроить? (y/N): ").strip().lower()
                if choice not in ['y', 'yes', 'д', 'да']:
                    print("Используйте существующую конфигурацию.")
                    return
    
    # Настройка бота
    bot_token = check_botfather_steps()
    if not bot_token:
        print("❌ Настройка прервана. Получите корректный токен от @BotFather.")
        return
    
    dest_chat = setup_channel()
    
    # Тестируем бота
    if test_bot_connection(bot_token) is False:
        print("❌ Не удалось подключиться к боту. Проверьте токен.")
        return
    
    # Сохраняем конфиг
    update_config(bot_token, dest_chat)
    
    print("\n🎉 Настройка завершена!")
    print("   📱 Теперь ваш основной Telegram в безопасности")
    print("   🤖 WebDAV будет работать через бота")
    
    show_next_steps()

if __name__ == "__main__":
    main() 