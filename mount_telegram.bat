@echo off
chcp 65001 >nul
REM Скрипт монтирования Telegram облака как диска

set RCLONE_PATH="C:/Program Files/rclone/rclone.exe"
set REMOTE_NAME=telegram
set DEFAULT_DRIVE=T:

if "%1"=="" (
    set DRIVE_LETTER=%DEFAULT_DRIVE%
) else (
    set DRIVE_LETTER=%1:
)

echo 💿 Монтирование Telegram облака как диска %DRIVE_LETTER%...

REM Проверяем WinFsp
if not exist "C:\Program Files\WinFsp\bin\winfsp-x64.dll" (
    if not exist "C:\Program Files (x86)\WinFsp\bin\winfsp-x64.dll" (
        echo ❌ WinFsp не установлен!
        echo Скачайте с https://winfsp.dev/rel/
        pause
        exit /b 1
    )
)

echo 🚀 Запуск монтирования...
echo ⚠️  Для остановки нажмите Ctrl+C

%RCLONE_PATH% mount %REMOTE_NAME%: %DRIVE_LETTER% ^
    --vfs-cache-mode writes ^
    --vfs-cache-max-size 1G ^
    --vfs-cache-max-age 1h ^
    --buffer-size 32M ^
    --dir-cache-time 5m ^
    --poll-interval 10s ^
    --timeout 30s ^
    --contimeout 10s ^
    --stats 30s ^
    --stats-one-line ^
    -v

echo 📤 Диск отмонтирован
pause
