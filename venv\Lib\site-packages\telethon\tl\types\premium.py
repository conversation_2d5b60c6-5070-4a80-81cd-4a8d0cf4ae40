"""File generated by TLObjects' generator. All changes will be ERASED"""
from ...tl.tlobject import TLObject
from typing import Optional, List, Union, TYPE_CHECKING
import os
import struct
from datetime import datetime
if TYPE_CHECKING:
    from ...tl.types import TypeBoost, TypeChat, TypeMyBoost, TypePrepaidGiveaway, TypeStatsPercentValue, TypeUser



class BoostsList(TLObject):
    CONSTRUCTOR_ID = 0x86f8613c
    SUBCLASS_OF_ID = 0x2235a8bd

    def __init__(self, count: int, boosts: List['TypeBoost'], users: List['TypeUser'], next_offset: Optional[str]=None):
        """
        Constructor for premium.BoostsList: Instance of BoostsList.
        """
        self.count = count
        self.boosts = boosts
        self.users = users
        self.next_offset = next_offset

    def to_dict(self):
        return {
            '_': 'BoostsList',
            'count': self.count,
            'boosts': [] if self.boosts is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.boosts],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users],
            'next_offset': self.next_offset
        }

    def _bytes(self):
        return b''.join((
            b'<a\xf8\x86',
            struct.pack('<I', (0 if self.next_offset is None or self.next_offset is False else 1)),
            struct.pack('<i', self.count),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.boosts)),b''.join(x._bytes() for x in self.boosts),
            b'' if self.next_offset is None or self.next_offset is False else (self.serialize_bytes(self.next_offset)),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _count = reader.read_int()
        reader.read_int()
        _boosts = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _boosts.append(_x)

        if flags & 1:
            _next_offset = reader.tgread_string()
        else:
            _next_offset = None
        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(count=_count, boosts=_boosts, users=_users, next_offset=_next_offset)


class BoostsStatus(TLObject):
    CONSTRUCTOR_ID = 0x4959427a
    SUBCLASS_OF_ID = 0xc31b1ab9

    def __init__(self, level: int, current_level_boosts: int, boosts: int, boost_url: str, my_boost: Optional[bool]=None, gift_boosts: Optional[int]=None, next_level_boosts: Optional[int]=None, premium_audience: Optional['TypeStatsPercentValue']=None, prepaid_giveaways: Optional[List['TypePrepaidGiveaway']]=None, my_boost_slots: Optional[List[int]]=None):
        """
        Constructor for premium.BoostsStatus: Instance of BoostsStatus.
        """
        self.level = level
        self.current_level_boosts = current_level_boosts
        self.boosts = boosts
        self.boost_url = boost_url
        self.my_boost = my_boost
        self.gift_boosts = gift_boosts
        self.next_level_boosts = next_level_boosts
        self.premium_audience = premium_audience
        self.prepaid_giveaways = prepaid_giveaways
        self.my_boost_slots = my_boost_slots

    def to_dict(self):
        return {
            '_': 'BoostsStatus',
            'level': self.level,
            'current_level_boosts': self.current_level_boosts,
            'boosts': self.boosts,
            'boost_url': self.boost_url,
            'my_boost': self.my_boost,
            'gift_boosts': self.gift_boosts,
            'next_level_boosts': self.next_level_boosts,
            'premium_audience': self.premium_audience.to_dict() if isinstance(self.premium_audience, TLObject) else self.premium_audience,
            'prepaid_giveaways': [] if self.prepaid_giveaways is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.prepaid_giveaways],
            'my_boost_slots': [] if self.my_boost_slots is None else self.my_boost_slots[:]
        }

    def _bytes(self):
        assert ((self.my_boost or self.my_boost is not None) and (self.my_boost_slots or self.my_boost_slots is not None)) or ((self.my_boost is None or self.my_boost is False) and (self.my_boost_slots is None or self.my_boost_slots is False)), 'my_boost, my_boost_slots parameters must all be False-y (like None) or all me True-y'
        return b''.join((
            b'zBYI',
            struct.pack('<I', (0 if self.my_boost is None or self.my_boost is False else 4) | (0 if self.gift_boosts is None or self.gift_boosts is False else 16) | (0 if self.next_level_boosts is None or self.next_level_boosts is False else 1) | (0 if self.premium_audience is None or self.premium_audience is False else 2) | (0 if self.prepaid_giveaways is None or self.prepaid_giveaways is False else 8) | (0 if self.my_boost_slots is None or self.my_boost_slots is False else 4)),
            struct.pack('<i', self.level),
            struct.pack('<i', self.current_level_boosts),
            struct.pack('<i', self.boosts),
            b'' if self.gift_boosts is None or self.gift_boosts is False else (struct.pack('<i', self.gift_boosts)),
            b'' if self.next_level_boosts is None or self.next_level_boosts is False else (struct.pack('<i', self.next_level_boosts)),
            b'' if self.premium_audience is None or self.premium_audience is False else (self.premium_audience._bytes()),
            self.serialize_bytes(self.boost_url),
            b'' if self.prepaid_giveaways is None or self.prepaid_giveaways is False else b''.join((b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.prepaid_giveaways)),b''.join(x._bytes() for x in self.prepaid_giveaways))),
            b'' if self.my_boost_slots is None or self.my_boost_slots is False else b''.join((b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.my_boost_slots)),b''.join(struct.pack('<i', x) for x in self.my_boost_slots))),
        ))

    @classmethod
    def from_reader(cls, reader):
        flags = reader.read_int()

        _my_boost = bool(flags & 4)
        _level = reader.read_int()
        _current_level_boosts = reader.read_int()
        _boosts = reader.read_int()
        if flags & 16:
            _gift_boosts = reader.read_int()
        else:
            _gift_boosts = None
        if flags & 1:
            _next_level_boosts = reader.read_int()
        else:
            _next_level_boosts = None
        if flags & 2:
            _premium_audience = reader.tgread_object()
        else:
            _premium_audience = None
        _boost_url = reader.tgread_string()
        if flags & 8:
            reader.read_int()
            _prepaid_giveaways = []
            for _ in range(reader.read_int()):
                _x = reader.tgread_object()
                _prepaid_giveaways.append(_x)

        else:
            _prepaid_giveaways = None
        if flags & 4:
            reader.read_int()
            _my_boost_slots = []
            for _ in range(reader.read_int()):
                _x = reader.read_int()
                _my_boost_slots.append(_x)

        else:
            _my_boost_slots = None
        return cls(level=_level, current_level_boosts=_current_level_boosts, boosts=_boosts, boost_url=_boost_url, my_boost=_my_boost, gift_boosts=_gift_boosts, next_level_boosts=_next_level_boosts, premium_audience=_premium_audience, prepaid_giveaways=_prepaid_giveaways, my_boost_slots=_my_boost_slots)


class MyBoosts(TLObject):
    CONSTRUCTOR_ID = 0x9ae228e2
    SUBCLASS_OF_ID = 0xad3512db

    def __init__(self, my_boosts: List['TypeMyBoost'], chats: List['TypeChat'], users: List['TypeUser']):
        """
        Constructor for premium.MyBoosts: Instance of MyBoosts.
        """
        self.my_boosts = my_boosts
        self.chats = chats
        self.users = users

    def to_dict(self):
        return {
            '_': 'MyBoosts',
            'my_boosts': [] if self.my_boosts is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.my_boosts],
            'chats': [] if self.chats is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.chats],
            'users': [] if self.users is None else [x.to_dict() if isinstance(x, TLObject) else x for x in self.users]
        }

    def _bytes(self):
        return b''.join((
            b'\xe2(\xe2\x9a',
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.my_boosts)),b''.join(x._bytes() for x in self.my_boosts),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.chats)),b''.join(x._bytes() for x in self.chats),
            b'\x15\xc4\xb5\x1c',struct.pack('<i', len(self.users)),b''.join(x._bytes() for x in self.users),
        ))

    @classmethod
    def from_reader(cls, reader):
        reader.read_int()
        _my_boosts = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _my_boosts.append(_x)

        reader.read_int()
        _chats = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _chats.append(_x)

        reader.read_int()
        _users = []
        for _ in range(reader.read_int()):
            _x = reader.tgread_object()
            _users.append(_x)

        return cls(my_boosts=_my_boosts, chats=_chats, users=_users)

